# Rules

## 1. Use Axios for HTTP requests

## 2. Use React Router for navigation

## 3. Use React Context for state management

## 4. Use Prisma for database access

## 5. Use Supabase tables for authentication and authorization, update the tables as along with auth informations (check for the prisma.schema file for the masteraccounts, users, organizers & admins tables)

## 6. Must use design patterns like OOP, Singleton, Adapter, Observer etc. and best practices

## 7. Must use TypeScript

## 8. Must follow MVC architecture

## 9. use pnpm command for client and npm command for server

## 10. **_ FOR ANY Media STORAGE for example, images, pdfs, videos, audio files, etc. MUST use Supabase Storage _**

## OAuth Implementation

## Ticket Types Organization by Event Categories

### Database Structure

- **Event Categories** (`eventcategories` table): Groups ticket types into logical categories

  - `category_id`: Primary key
  - `event_id`: Links to specific event
  - `name`: Category name (e.g., "VIP", "General Admission", "Student")
  - `description`: Category description
  - `category_type`: Type of category (e.g., "general", "premium", "special")

- **Ticket Types** (`tickettypes` table): Individual ticket types within categories
  - `ticket_type_id`: Primary key
  - `event_id`: Links to specific event
  - `category_id`: Links to event category (foreign key)
  - `name`: Ticket type name
  - `description`: Ticket description
  - `price`: Ticket price
  - `quantity_available`: Available quantity
  - `max_per_order`: Maximum tickets per order

### Frontend Implementation with Tab Content

#### Event Detail Page Structure

```jsx
// Category Selection Tabs
<Tabs value={selectedCategory} onValueChange={setSelectedCategory}>
  <TabsList className="w-full">
    {eventCategories.map((category) => (
      <TabsTrigger key={category.id} value={category.id.toString()}>
        {category.name}
      </TabsTrigger>
    ))}
  </TabsList>

  {eventCategories.map((category) => (
    <TabsContent key={category.id} value={category.id.toString()}>
      {/* Display ticket types for this category */}
      {ticketTypes
        .filter((ticket) => ticket.category_id === category.id)
        .map((ticket) => (
          <TicketTypeCard key={ticket.id} ticket={ticket} />
        ))}
    </TabsContent>
  ))}
</Tabs>
```

#### Category-Based Organization Examples

**VIP Category Tab:**

- VIP General Access ($150)
- VIP Meet & Greet ($250)
- VIP Premium Package ($350)

**General Admission Tab:**

- Early Bird General ($75)
- Regular General ($100)
- Late Entry General ($125)

**Student Category Tab:**

- Student Discount ($50)
- Student Group (4+) ($45 each)

**Special Access Tab:**

- Wheelchair Accessible ($100)
- Senior Citizen ($80)
- Military Discount ($85)

### Implementation Guidelines

1. **Always group ticket types by their event categories**
2. **Use TabsContent component to separate categories visually**
3. **Filter ticket types by category_id when displaying**
4. **Maintain category-ticket relationship in database**
5. **Allow organizers to create custom categories during event creation**
6. **Display category descriptions to help users understand differences**

### API Endpoints Structure

- `GET /api/events/:id` - Returns event with categories and ticket types
- Categories should include their associated ticket types
- Ticket types should reference their parent category

## After each task completion, register what has been donee breifly in the Changelog.md file
