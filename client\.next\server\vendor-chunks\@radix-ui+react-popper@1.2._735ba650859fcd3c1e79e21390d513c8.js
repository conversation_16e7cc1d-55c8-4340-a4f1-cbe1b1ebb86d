"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-popper@1.2._735ba650859fcd3c1e79e21390d513c8";
exports.ids = ["vendor-chunks/@radix-ui+react-popper@1.2._735ba650859fcd3c1e79e21390d513c8"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-popper@1.2._735ba650859fcd3c1e79e21390d513c8/node_modules/@radix-ui/react-popper/dist/index.mjs":
/*!********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-popper@1.2._735ba650859fcd3c1e79e21390d513c8/node_modules/@radix-ui/react-popper/dist/index.mjs ***!
  \********************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ALIGN_OPTIONS: () => (/* binding */ ALIGN_OPTIONS),\n/* harmony export */   Anchor: () => (/* binding */ Anchor),\n/* harmony export */   Arrow: () => (/* binding */ Arrow),\n/* harmony export */   Content: () => (/* binding */ Content),\n/* harmony export */   Popper: () => (/* binding */ Popper),\n/* harmony export */   PopperAnchor: () => (/* binding */ PopperAnchor),\n/* harmony export */   PopperArrow: () => (/* binding */ PopperArrow),\n/* harmony export */   PopperContent: () => (/* binding */ PopperContent),\n/* harmony export */   Root: () => (/* binding */ Root2),\n/* harmony export */   SIDE_OPTIONS: () => (/* binding */ SIDE_OPTIONS),\n/* harmony export */   createPopperScope: () => (/* binding */ createPopperScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @floating-ui/react-dom */ \"(ssr)/./node_modules/.pnpm/@floating-ui+react-dom@2.1._21166bf695a70271b11ec47713a78d3f/node_modules/@floating-ui/react-dom/dist/floating-ui.react-dom.mjs\");\n/* harmony import */ var _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @floating-ui/react-dom */ \"(ssr)/./node_modules/.pnpm/@floating-ui+dom@1.7.1/node_modules/@floating-ui/dom/dist/floating-ui.dom.mjs\");\n/* harmony import */ var _radix_ui_react_arrow__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-arrow */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-arrow@1.1.1_8bbd87e91ae262ef454f9567efd09b52/node_modules/@radix-ui/react-arrow/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-compose-ref_2a0e526a8f7e7aada080206d385bb572/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-context@1.1_4fe40d510edca7ae4ca9c92afeb1ae6d/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2_997b35f2e2aa9d3174fc03a0f79e437b/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-callbac_2dc63ba6354ec7ef7d955ba47145829a/node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-layout-_7d9c308966eafc0f645092b629b133a3/node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_size__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-use-size */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-size@1._25ea7b66547079fee23d7c838aabe8ed/node_modules/@radix-ui/react-use-size/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ ALIGN_OPTIONS,Anchor,Arrow,Content,Popper,PopperAnchor,PopperArrow,PopperContent,Root,SIDE_OPTIONS,createPopperScope auto */ // packages/react/popper/src/Popper.tsx\n\n\n\n\n\n\n\n\n\n\nvar SIDE_OPTIONS = [\n    \"top\",\n    \"right\",\n    \"bottom\",\n    \"left\"\n];\nvar ALIGN_OPTIONS = [\n    \"start\",\n    \"center\",\n    \"end\"\n];\nvar POPPER_NAME = \"Popper\";\nvar [createPopperContext, createPopperScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(POPPER_NAME);\nvar [PopperProvider, usePopperContext] = createPopperContext(POPPER_NAME);\nvar Popper = (props)=>{\n    const { __scopePopper, children } = props;\n    const [anchor, setAnchor] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PopperProvider, {\n        scope: __scopePopper,\n        anchor,\n        onAnchorChange: setAnchor,\n        children\n    });\n};\nPopper.displayName = POPPER_NAME;\nvar ANCHOR_NAME = \"PopperAnchor\";\nvar PopperAnchor = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopePopper, virtualRef, ...anchorProps } = props;\n    const context = usePopperContext(ANCHOR_NAME, __scopePopper);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, ref);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"PopperAnchor.useEffect\": ()=>{\n            context.onAnchorChange(virtualRef?.current || ref.current);\n        }\n    }[\"PopperAnchor.useEffect\"]);\n    return virtualRef ? null : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        ...anchorProps,\n        ref: composedRefs\n    });\n});\nPopperAnchor.displayName = ANCHOR_NAME;\nvar CONTENT_NAME = \"PopperContent\";\nvar [PopperContentProvider, useContentContext] = createPopperContext(CONTENT_NAME);\nvar PopperContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopePopper, side = \"bottom\", sideOffset = 0, align = \"center\", alignOffset = 0, arrowPadding = 0, avoidCollisions = true, collisionBoundary = [], collisionPadding: collisionPaddingProp = 0, sticky = \"partial\", hideWhenDetached = false, updatePositionStrategy = \"optimized\", onPlaced, ...contentProps } = props;\n    const context = usePopperContext(CONTENT_NAME, __scopePopper);\n    const [content, setContent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, {\n        \"PopperContent.useComposedRefs[composedRefs]\": (node)=>setContent(node)\n    }[\"PopperContent.useComposedRefs[composedRefs]\"]);\n    const [arrow, setArrow] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const arrowSize = (0,_radix_ui_react_use_size__WEBPACK_IMPORTED_MODULE_5__.useSize)(arrow);\n    const arrowWidth = arrowSize?.width ?? 0;\n    const arrowHeight = arrowSize?.height ?? 0;\n    const desiredPlacement = side + (align !== \"center\" ? \"-\" + align : \"\");\n    const collisionPadding = typeof collisionPaddingProp === \"number\" ? collisionPaddingProp : {\n        top: 0,\n        right: 0,\n        bottom: 0,\n        left: 0,\n        ...collisionPaddingProp\n    };\n    const boundary = Array.isArray(collisionBoundary) ? collisionBoundary : [\n        collisionBoundary\n    ];\n    const hasExplicitBoundaries = boundary.length > 0;\n    const detectOverflowOptions = {\n        padding: collisionPadding,\n        boundary: boundary.filter(isNotNull),\n        // with `strategy: 'fixed'`, this is the only way to get it to respect boundaries\n        altBoundary: hasExplicitBoundaries\n    };\n    const { refs, floatingStyles, placement, isPositioned, middlewareData } = (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.useFloating)({\n        // default to `fixed` strategy so users don't have to pick and we also avoid focus scroll issues\n        strategy: \"fixed\",\n        placement: desiredPlacement,\n        whileElementsMounted: {\n            \"PopperContent.useFloating\": (...args)=>{\n                const cleanup = (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_7__.autoUpdate)(...args, {\n                    animationFrame: updatePositionStrategy === \"always\"\n                });\n                return cleanup;\n            }\n        }[\"PopperContent.useFloating\"],\n        elements: {\n            reference: context.anchor\n        },\n        middleware: [\n            (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.offset)({\n                mainAxis: sideOffset + arrowHeight,\n                alignmentAxis: alignOffset\n            }),\n            avoidCollisions && (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.shift)({\n                mainAxis: true,\n                crossAxis: false,\n                limiter: sticky === \"partial\" ? (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.limitShift)() : void 0,\n                ...detectOverflowOptions\n            }),\n            avoidCollisions && (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.flip)({\n                ...detectOverflowOptions\n            }),\n            (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.size)({\n                ...detectOverflowOptions,\n                apply: {\n                    \"PopperContent.useFloating\": ({ elements, rects, availableWidth, availableHeight })=>{\n                        const { width: anchorWidth, height: anchorHeight } = rects.reference;\n                        const contentStyle = elements.floating.style;\n                        contentStyle.setProperty(\"--radix-popper-available-width\", `${availableWidth}px`);\n                        contentStyle.setProperty(\"--radix-popper-available-height\", `${availableHeight}px`);\n                        contentStyle.setProperty(\"--radix-popper-anchor-width\", `${anchorWidth}px`);\n                        contentStyle.setProperty(\"--radix-popper-anchor-height\", `${anchorHeight}px`);\n                    }\n                }[\"PopperContent.useFloating\"]\n            }),\n            arrow && (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.arrow)({\n                element: arrow,\n                padding: arrowPadding\n            }),\n            transformOrigin({\n                arrowWidth,\n                arrowHeight\n            }),\n            hideWhenDetached && (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.hide)({\n                strategy: \"referenceHidden\",\n                ...detectOverflowOptions\n            })\n        ]\n    });\n    const [placedSide, placedAlign] = getSideAndAlignFromPlacement(placement);\n    const handlePlaced = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onPlaced);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_9__.useLayoutEffect)({\n        \"PopperContent.useLayoutEffect\": ()=>{\n            if (isPositioned) {\n                handlePlaced?.();\n            }\n        }\n    }[\"PopperContent.useLayoutEffect\"], [\n        isPositioned,\n        handlePlaced\n    ]);\n    const arrowX = middlewareData.arrow?.x;\n    const arrowY = middlewareData.arrow?.y;\n    const cannotCenterArrow = middlewareData.arrow?.centerOffset !== 0;\n    const [contentZIndex, setContentZIndex] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_9__.useLayoutEffect)({\n        \"PopperContent.useLayoutEffect\": ()=>{\n            if (content) setContentZIndex(window.getComputedStyle(content).zIndex);\n        }\n    }[\"PopperContent.useLayoutEffect\"], [\n        content\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"div\", {\n        ref: refs.setFloating,\n        \"data-radix-popper-content-wrapper\": \"\",\n        style: {\n            ...floatingStyles,\n            transform: isPositioned ? floatingStyles.transform : \"translate(0, -200%)\",\n            // keep off the page when measuring\n            minWidth: \"max-content\",\n            zIndex: contentZIndex,\n            [\"--radix-popper-transform-origin\"]: [\n                middlewareData.transformOrigin?.x,\n                middlewareData.transformOrigin?.y\n            ].join(\" \"),\n            // hide the content if using the hide middleware and should be hidden\n            // set visibility to hidden and disable pointer events so the UI behaves\n            // as if the PopperContent isn't there at all\n            ...middlewareData.hide?.referenceHidden && {\n                visibility: \"hidden\",\n                pointerEvents: \"none\"\n            }\n        },\n        dir: props.dir,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PopperContentProvider, {\n            scope: __scopePopper,\n            placedSide,\n            onArrowChange: setArrow,\n            arrowX,\n            arrowY,\n            shouldHideArrow: cannotCenterArrow,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n                \"data-side\": placedSide,\n                \"data-align\": placedAlign,\n                ...contentProps,\n                ref: composedRefs,\n                style: {\n                    ...contentProps.style,\n                    // if the PopperContent hasn't been placed yet (not all measurements done)\n                    // we prevent animations so that users's animation don't kick in too early referring wrong sides\n                    animation: !isPositioned ? \"none\" : void 0\n                }\n            })\n        })\n    });\n});\nPopperContent.displayName = CONTENT_NAME;\nvar ARROW_NAME = \"PopperArrow\";\nvar OPPOSITE_SIDE = {\n    top: \"bottom\",\n    right: \"left\",\n    bottom: \"top\",\n    left: \"right\"\n};\nvar PopperArrow = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(function PopperArrow2(props, forwardedRef) {\n    const { __scopePopper, ...arrowProps } = props;\n    const contentContext = useContentContext(ARROW_NAME, __scopePopper);\n    const baseSide = OPPOSITE_SIDE[contentContext.placedSide];\n    return(// we have to use an extra wrapper because `ResizeObserver` (used by `useSize`)\n    // doesn't report size as we'd expect on SVG elements.\n    // it reports their bounding box which is effectively the largest path inside the SVG.\n    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"span\", {\n        ref: contentContext.onArrowChange,\n        style: {\n            position: \"absolute\",\n            left: contentContext.arrowX,\n            top: contentContext.arrowY,\n            [baseSide]: 0,\n            transformOrigin: {\n                top: \"\",\n                right: \"0 0\",\n                bottom: \"center 0\",\n                left: \"100% 0\"\n            }[contentContext.placedSide],\n            transform: {\n                top: \"translateY(100%)\",\n                right: \"translateY(50%) rotate(90deg) translateX(-50%)\",\n                bottom: `rotate(180deg)`,\n                left: \"translateY(50%) rotate(-90deg) translateX(50%)\"\n            }[contentContext.placedSide],\n            visibility: contentContext.shouldHideArrow ? \"hidden\" : void 0\n        },\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_arrow__WEBPACK_IMPORTED_MODULE_10__.Root, {\n            ...arrowProps,\n            ref: forwardedRef,\n            style: {\n                ...arrowProps.style,\n                // ensures the element can be measured correctly (mostly for if SVG)\n                display: \"block\"\n            }\n        })\n    }));\n});\nPopperArrow.displayName = ARROW_NAME;\nfunction isNotNull(value) {\n    return value !== null;\n}\nvar transformOrigin = (options)=>({\n        name: \"transformOrigin\",\n        options,\n        fn (data) {\n            const { placement, rects, middlewareData } = data;\n            const cannotCenterArrow = middlewareData.arrow?.centerOffset !== 0;\n            const isArrowHidden = cannotCenterArrow;\n            const arrowWidth = isArrowHidden ? 0 : options.arrowWidth;\n            const arrowHeight = isArrowHidden ? 0 : options.arrowHeight;\n            const [placedSide, placedAlign] = getSideAndAlignFromPlacement(placement);\n            const noArrowAlign = {\n                start: \"0%\",\n                center: \"50%\",\n                end: \"100%\"\n            }[placedAlign];\n            const arrowXCenter = (middlewareData.arrow?.x ?? 0) + arrowWidth / 2;\n            const arrowYCenter = (middlewareData.arrow?.y ?? 0) + arrowHeight / 2;\n            let x = \"\";\n            let y = \"\";\n            if (placedSide === \"bottom\") {\n                x = isArrowHidden ? noArrowAlign : `${arrowXCenter}px`;\n                y = `${-arrowHeight}px`;\n            } else if (placedSide === \"top\") {\n                x = isArrowHidden ? noArrowAlign : `${arrowXCenter}px`;\n                y = `${rects.floating.height + arrowHeight}px`;\n            } else if (placedSide === \"right\") {\n                x = `${-arrowHeight}px`;\n                y = isArrowHidden ? noArrowAlign : `${arrowYCenter}px`;\n            } else if (placedSide === \"left\") {\n                x = `${rects.floating.width + arrowHeight}px`;\n                y = isArrowHidden ? noArrowAlign : `${arrowYCenter}px`;\n            }\n            return {\n                data: {\n                    x,\n                    y\n                }\n            };\n        }\n    });\nfunction getSideAndAlignFromPlacement(placement) {\n    const [side, align = \"center\"] = placement.split(\"-\");\n    return [\n        side,\n        align\n    ];\n}\nvar Root2 = Popper;\nvar Anchor = PopperAnchor;\nvar Content = PopperContent;\nvar Arrow = PopperArrow;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-popper@1.2._735ba650859fcd3c1e79e21390d513c8/node_modules/@radix-ui/react-popper/dist/index.mjs\n");

/***/ })

};
;