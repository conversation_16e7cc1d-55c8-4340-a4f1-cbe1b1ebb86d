"use client"

import { useState, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { useRouter } from "next/navigation"
import { Button } from "@/components/ui/button"
import { ChevronLeft, ChevronRight, Calendar, MapPin } from "lucide-react"

export default function HeroCarousel({ events, loading = false, error = null }) {
  const router = useRouter()
  const [currentIndex, setCurrentIndex] = useState(0)
  const [direction, setDirection] = useState(1) // 1 for right, -1 for left

  // Helper functions to handle both mock and real data
  const getEventImage = (event) => {
    return event?.bannerImage || event?.image || `/placeholder.svg?height=700&width=1600`
  }

  const getEventGenre = (event) => {
    if (typeof event?.genre === 'string') {
      return event.genre
    }
    return event?.genre?.name || 'Event'
  }

  const getEventDate = (event) => {
    return event?.startDate || event?.date
  }

  const getEventLocation = (event) => {
    // Handle mock data structure
    if (event?.location && typeof event.location === 'object' && event.location.venue) {
      return {
        venue: event.location.venue,
        city: event.location.city
      }
    }

    // Handle real database structure
    const venue = event?.venueName || event?.location?.venueName || 'TBA'
    const city = event?.location?.city || 'TBA'

    return { venue, city }
  }

  useEffect(() => {
    if (!events || events.length === 0) return

    const interval = setInterval(() => {
      handleNext()
    }, 5000)

    return () => clearInterval(interval)
  }, [currentIndex, events?.length])

  const handlePrev = () => {
    if (!events || events.length === 0) return
    setDirection(-1)
    setCurrentIndex((prevIndex) => (prevIndex === 0 ? events.length - 1 : prevIndex - 1))
  }

  const handleNext = () => {
    if (!events || events.length === 0) return
    setDirection(1)
    setCurrentIndex((prevIndex) => (prevIndex === events.length - 1 ? 0 : prevIndex + 1))
  }

  // Loading state
  if (loading) {
    return (
      <div className="relative h-[70vh] overflow-hidden bg-zinc-800 animate-pulse">
        <div className="absolute inset-0 bg-zinc-700"></div>
        <div className="absolute bottom-0 left-0 right-0 p-6 md:p-12">
          <div className="container mx-auto">
            <div className="max-w-2xl space-y-4">
              <div className="flex items-center space-x-2">
                <div className="h-6 w-16 bg-zinc-600 rounded-full"></div>
                <div className="h-4 w-24 bg-zinc-600 rounded"></div>
              </div>
              <div className="h-12 w-3/4 bg-zinc-600 rounded"></div>
              <div className="h-4 w-1/2 bg-zinc-600 rounded"></div>
              <div className="h-10 w-32 bg-zinc-600 rounded"></div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Error state
  if (error) {
    return (
      <div className="relative h-[70vh] overflow-hidden bg-zinc-900 flex items-center justify-center">
        <div className="text-center text-red-400">
          <svg className="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <h3 className="text-lg font-semibold mb-2">Failed to load featured events</h3>
          <p className="text-zinc-400">{error}</p>
        </div>
      </div>
    )
  }

  // Empty state
  if (!events || events.length === 0) {
    return (
      <div className="relative h-[70vh] overflow-hidden bg-zinc-900 flex items-center justify-center">
        <div className="text-center text-zinc-400">
          <svg className="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 4v10m6-10v10m-6-4h6" />
          </svg>
          <h3 className="text-lg font-semibold mb-2">No Live Events</h3>
          <p className="text-zinc-400">There are no live events with active ticket sales at the moment.</p>
          <p className="text-zinc-300 text-sm mt-2">Check back soon or browse upcoming events!</p>
        </div>
      </div>
    )
  }

  const variants = {
    enter: (direction) => ({
      x: direction > 0 ? 1000 : -1000,
      opacity: 0,
    }),
    center: {
      x: 0,
      opacity: 1,
    },
    exit: (direction) => ({
      x: direction > 0 ? -1000 : 1000,
      opacity: 0,
    }),
  }

  const handleEventClick = (eventId) => {
    router.push(`/events/${eventId}`)
  }

  return (
    <div className="relative h-[70vh] overflow-hidden">
      <AnimatePresence initial={false} custom={direction}>
        <motion.div
          key={currentIndex}
          custom={direction}
          variants={variants}
          initial="enter"
          animate="center"
          exit="exit"
          transition={{ type: "tween", duration: 0.5 }}
          className="absolute inset-0"
        >
          <div className="relative h-full w-full">
            <img
              src={getEventImage(events[currentIndex])}
              alt={events[currentIndex].title}
              className="h-full w-full object-cover"
              onError={(e) => {
                e.target.src = `/placeholder.svg?height=700&width=1600`
              }}
            />
            <div className="absolute inset-0 bg-gradient-to-t from-zinc-950 via-zinc-950/50 to-transparent"></div>

            <div className="absolute bottom-0 left-0 right-0 p-6 md:p-12">
              <div className="container mx-auto">
                <div className="max-w-2xl">
                  <div className="flex items-center space-x-2 mb-3">
                    <span className="bg-red-600 text-white text-xs px-2 py-1 rounded-full uppercase">
                      {getEventGenre(events[currentIndex])}
                    </span>
                    <span className="text-zinc-300 text-sm flex items-center">
                      <Calendar className="h-3 w-3 mr-1" />
                      {new Date(getEventDate(events[currentIndex])).toLocaleDateString("en-US", {
                        month: "short",
                        day: "numeric",
                        year: "numeric",
                      })}
                    </span>
                  </div>

                  <h2 className="text-3xl md:text-5xl font-bold mb-3">{events[currentIndex].title}</h2>

                  <div className="flex items-center text-zinc-300 mb-6">
                    <MapPin className="h-4 w-4 mr-1" />
                    <span>
                      {(() => {
                        const location = getEventLocation(events[currentIndex])
                        return `${location.venue}, ${location.city}`
                      })()}
                    </span>
                  </div>

                  <Button
                    size="lg"
                    className="bg-red-600 hover:bg-red-700"
                    onClick={() => handleEventClick(events[currentIndex].id)}
                  >
                    Get Tickets
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      </AnimatePresence>

      <button
        className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white p-2 rounded-full z-10"
        onClick={handlePrev}
      >
        <ChevronLeft className="h-6 w-6" />
      </button>

      <button
        className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white p-2 rounded-full z-10"
        onClick={handleNext}
      >
        <ChevronRight className="h-6 w-6" />
      </button>

      <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2 z-10">
        {events.map((_, index) => (
          <button
            key={index}
            className={`h-2 w-2 rounded-full ${index === currentIndex ? "bg-red-600" : "bg-white/50"}`}
            onClick={() => {
              setDirection(index > currentIndex ? 1 : -1)
              setCurrentIndex(index)
            }}
          />
        ))}
      </div>
    </div>
  )
}
