"use client"

import { motion } from "framer-motion"
import { useRouter } from "next/navigation"
import { Calendar, Clock, MapPin } from "lucide-react"

export default function EventGrid({ events, loading = false, error = null }) {
  const router = useRouter()

  const handleEventClick = (eventId) => {
    router.push(`/events/${eventId}`)
  }

  // Helper function to get event date (handles both mock and real data)
  const getEventDate = (event) => {
    return event.startDate || event.date
  }

  // Helper function to get event time (handles both mock and real data)
  const getEventTime = (event) => {
    if (event.startTime) {
      // Convert time string to readable format
      try {
        const timeStr = event.startTime.includes('T') ? event.startTime : `1970-01-01T${event.startTime}`
        return new Date(timeStr).toLocaleTimeString("en-US", {
          hour: "numeric",
          minute: "2-digit",
          hour12: true
        })
      } catch (error) {
        return "7:00 PM"
      }
    }
    return event.time || "7:00 PM"
  }

  // Helper function to get event genre (handles both mock and real data)
  const getEventGenre = (event) => {
    if (typeof event.genre === 'string') {
      return event.genre
    }
    return event.genre?.name || 'Event'
  }

  // Helper function to get event location (handles both mock and real data)
  const getEventLocation = (event) => {
    // Handle mock data structure
    if (event.location && typeof event.location === 'object' && event.location.venue) {
      return {
        venue: event.location.venue,
        city: event.location.city
      }
    }

    // Handle real database structure
    const venue = event.venueName || event.location?.venueName || 'TBA'
    const city = event.location?.city || 'TBA'

    return { venue, city }
  }

  // Helper function to get event price (handles both mock and real data)
  const getEventPrice = (event) => {
    // Handle mock data structure
    if (typeof event.price === 'number') {
      return event.price.toFixed(2)
    }

    // Handle real database structure - get minimum ticket price
    if (event.ticketTypes && event.ticketTypes.length > 0) {
      const minPrice = Math.min(...event.ticketTypes.map(tt => parseFloat(tt.price) || 0))
      return minPrice.toFixed(2)
    }

    return "49.99"
  }

  // Helper function to get event image (handles both mock and real data)
  const getEventImage = (event) => {
    return event.bannerImage || event.image || `/placeholder.svg?height=160&width=300`
  }

  const container = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  }

  const item = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0 },
  }

  // Loading state
  if (loading) {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        {[...Array(8)].map((_, index) => (
          <div key={index} className="bg-zinc-800 rounded-lg overflow-hidden animate-pulse">
            <div className="h-40 bg-zinc-700"></div>
            <div className="p-4 space-y-3">
              <div className="h-4 bg-zinc-700 rounded w-3/4"></div>
              <div className="space-y-2">
                <div className="h-3 bg-zinc-700 rounded w-1/2"></div>
                <div className="h-3 bg-zinc-700 rounded w-1/3"></div>
                <div className="h-3 bg-zinc-700 rounded w-2/3"></div>
              </div>
              <div className="flex justify-between">
                <div className="h-4 bg-zinc-700 rounded w-1/4"></div>
                <div className="h-3 bg-zinc-700 rounded w-1/3"></div>
              </div>
            </div>
          </div>
        ))}
      </div>
    )
  }

  // Error state
  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-400 mb-4">
          <svg className="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <h3 className="text-lg font-semibold mb-2">Failed to load events</h3>
          <p className="text-zinc-400">{error}</p>
        </div>
      </div>
    )
  }

  // Empty state
  if (!events || events.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-zinc-400 mb-4">
          <svg className="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 4v10m6-10v10m-6-4h6" />
          </svg>
          <h3 className="text-lg font-semibold mb-2">No events found</h3>
          <p className="text-zinc-400">There are no events to display at the moment.</p>
        </div>
      </div>
    )
  }

  return (
    <motion.div
      className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6"
      variants={container}
      initial="hidden"
      animate="show"
    >
      {events.map((event) => {
        const eventDate = getEventDate(event)
        const eventTime = getEventTime(event)
        const eventGenre = getEventGenre(event)
        const eventLocation = getEventLocation(event)
        const eventPrice = getEventPrice(event)
        const eventImage = getEventImage(event)

        return (
          <motion.div
            key={event.id}
            variants={item}
            whileHover={{ y: -5 }}
            className="bg-zinc-800 rounded-lg overflow-hidden cursor-pointer"
            onClick={() => handleEventClick(event.id)}
          >
            <div className="h-40 relative">
              <img
                src={eventImage}
                alt={event.title}
                className="w-full h-full object-cover"
                onError={(e) => {
                  e.target.src = `/placeholder.svg?height=160&width=300`
                }}
              />
              <div className="absolute top-0 right-0 bg-red-600 text-white px-2 py-1 text-xs">
                {eventGenre}
              </div>
            </div>

            <div className="p-4">
              <h3 className="font-bold mb-2 line-clamp-1">{event.title}</h3>

              <div className="space-y-1 mb-3">
                <div className="flex items-center text-zinc-400 text-xs">
                  <Calendar className="h-3 w-3 mr-1" />
                  <span>
                    {new Date(eventDate).toLocaleDateString("en-US", {
                      month: "short",
                      day: "numeric",
                      year: "numeric",
                    })}
                  </span>
                </div>
                <div className="flex items-center text-zinc-400 text-xs">
                  <Clock className="h-3 w-3 mr-1" />
                  <span>{eventTime}</span>
                </div>
                <div className="flex items-center text-zinc-400 text-xs">
                  <MapPin className="h-3 w-3 mr-1" />
                  <span className="truncate">
                    {eventLocation.venue}, {eventLocation.city}
                  </span>
                </div>
              </div>

              <div className="flex justify-between items-center">
                <span className="text-sm font-bold">${eventPrice}</span>
                <span className="text-xs text-zinc-400">Starting from</span>
              </div>
            </div>
          </motion.div>
        )
      })}
    </motion.div>
  )
}
