"use client"

import { useState } from "react"
import { useAdmin } from "@/context/admin-context"
import AdminLayout from "@/components/admin/admin-layout"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Users, Search, MoreVertical, Shield, Building2, Mail, Calendar, Activity } from "lucide-react"
import { useToast } from "@/hooks/use-toast"

export default function UsersManagement() {
  const { admin, loading } = useAdmin()
  const { toast } = useToast()
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedTab, setSelectedTab] = useState("all")

  // Mock data - in real app, this would come from API
  const [users, setUsers] = useState([
    {
      id: "1",
      name: "<PERSON> Doe",
      email: "<EMAIL>",
      role: "user",
      status: "active",
      joinDate: "2024-01-15",
      lastActive: "2 hours ago",
      eventsAttended: 12,
      totalSpent: "$450",
    },
    {
      id: "2",
      name: "Sarah Johnson",
      email: "<EMAIL>",
      role: "organizer",
      status: "active",
      joinDate: "2023-11-20",
      lastActive: "1 day ago",
      eventsCreated: 8,
      totalRevenue: "$12,400",
    },
    {
      id: "3",
      name: "Admin User",
      email: "<EMAIL>",
      role: "admin",
      status: "active",
      joinDate: "2023-01-01",
      lastActive: "Online",
      permissions: "Full Access",
    },
    {
      id: "4",
      name: "Mike Wilson",
      email: "<EMAIL>",
      role: "user",
      status: "suspended",
      joinDate: "2024-02-10",
      lastActive: "1 week ago",
      eventsAttended: 3,
      totalSpent: "$120",
    },
    {
      id: "5",
      name: "Emma Davis",
      email: "<EMAIL>",
      role: "organizer",
      status: "active",
      joinDate: "2023-12-05",
      lastActive: "3 hours ago",
      eventsCreated: 15,
      totalRevenue: "$28,900",
    },
  ])

  if (loading) {
    return (
      <div className="min-h-screen bg-zinc-950 flex items-center justify-center">
        <div className="text-white">Loading...</div>
      </div>
    )
  }

  if (!admin) {
    return (
      <div className="min-h-screen bg-zinc-950 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-white mb-4">Access Denied</h1>
          <p className="text-zinc-400">You need admin privileges to access this page.</p>
        </div>
      </div>
    )
  }

  const handleRoleChange = (userId, newRole) => {
    setUsers(users.map((user) => (user.id === userId ? { ...user, role: newRole } : user)))
    toast({
      title: "Role Updated",
      description: `User role changed to ${newRole}`,
    })
  }

  const handleStatusChange = (userId, newStatus) => {
    setUsers(users.map((user) => (user.id === userId ? { ...user, status: newStatus } : user)))
    toast({
      title: "Status Updated",
      description: `User ${newStatus === "active" ? "activated" : "suspended"}`,
    })
  }

  const filteredUsers = users.filter((user) => {
    const matchesSearch =
      user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesTab = selectedTab === "all" || user.role === selectedTab
    return matchesSearch && matchesTab
  })

  const getUserStats = (role) => {
    return users.filter((user) => (role === "all" ? true : user.role === role)).length
  }

  const getRoleIcon = (role) => {
    switch (role) {
      case "admin":
        return <Shield className="h-4 w-4" />
      case "organizer":
        return <Building2 className="h-4 w-4" />
      default:
        return <Users className="h-4 w-4" />
    }
  }

  const getRoleBadgeColor = (role) => {
    switch (role) {
      case "admin":
        return "bg-red-500/20 text-red-500 border-red-500/30"
      case "organizer":
        return "bg-purple-500/20 text-purple-500 border-purple-500/30"
      default:
        return "bg-blue-500/20 text-blue-500 border-blue-500/30"
    }
  }

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-white">User Management</h1>
            <p className="text-zinc-400 mt-1">Manage users, organizers, and administrators</p>
          </div>
        </div>

        {/* Search */}
        <div className="relative max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-zinc-400 h-4 w-4" />
          <Input
            placeholder="Search users..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 bg-zinc-900 border-zinc-800 text-white"
          />
        </div>

        {/* Tabs */}
        <Tabs value={selectedTab} onValueChange={setSelectedTab}>
          <TabsList className="bg-zinc-900 border-zinc-800">
            <TabsTrigger value="all" className="data-[state=active]:bg-red-600">
              All Users ({getUserStats("all")})
            </TabsTrigger>
            <TabsTrigger value="user" className="data-[state=active]:bg-red-600">
              Users ({getUserStats("user")})
            </TabsTrigger>
            <TabsTrigger value="organizer" className="data-[state=active]:bg-red-600">
              Organizers ({getUserStats("organizer")})
            </TabsTrigger>
            <TabsTrigger value="admin" className="data-[state=active]:bg-red-600">
              Admins ({getUserStats("admin")})
            </TabsTrigger>
          </TabsList>

          <TabsContent value={selectedTab} className="mt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredUsers.map((user) => (
                <Card key={user.id} className="bg-zinc-900 border-zinc-800">
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-3">
                        <div className="w-12 h-12 bg-zinc-800 rounded-full flex items-center justify-center">
                          {getRoleIcon(user.role)}
                        </div>
                        <div>
                          <CardTitle className="text-white text-lg">{user.name}</CardTitle>
                          <div className="flex items-center gap-2 mt-1">
                            <Mail className="h-3 w-3 text-zinc-400" />
                            <span className="text-sm text-zinc-400">{user.email}</span>
                          </div>
                        </div>
                      </div>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm" className="text-zinc-400 hover:text-white">
                            <MoreVertical className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent className="bg-zinc-800 border-zinc-700">
                          <DropdownMenuItem onClick={() => handleRoleChange(user.id, "user")}>
                            Change to User
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleRoleChange(user.id, "organizer")}>
                            Change to Organizer
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleRoleChange(user.id, "admin")}>
                            Change to Admin
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() =>
                              handleStatusChange(user.id, user.status === "active" ? "suspended" : "active")
                            }
                          >
                            {user.status === "active" ? "Suspend User" : "Activate User"}
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <Badge className={getRoleBadgeColor(user.role)}>
                          {user.role.charAt(0).toUpperCase() + user.role.slice(1)}
                        </Badge>
                        <Badge variant={user.status === "active" ? "default" : "destructive"}>{user.status}</Badge>
                      </div>

                      <div className="space-y-2 text-sm">
                        <div className="flex items-center gap-2 text-zinc-400">
                          <Calendar className="h-3 w-3" />
                          <span>Joined {user.joinDate}</span>
                        </div>
                        <div className="flex items-center gap-2 text-zinc-400">
                          <Activity className="h-3 w-3" />
                          <span>Last active {user.lastActive}</span>
                        </div>
                      </div>

                      {user.role === "user" && (
                        <div className="pt-2 border-t border-zinc-800">
                          <div className="grid grid-cols-2 gap-4 text-sm">
                            <div>
                              <p className="text-zinc-400">Events Attended</p>
                              <p className="text-white font-medium">{user.eventsAttended}</p>
                            </div>
                            <div>
                              <p className="text-zinc-400">Total Spent</p>
                              <p className="text-white font-medium">{user.totalSpent}</p>
                            </div>
                          </div>
                        </div>
                      )}

                      {user.role === "organizer" && (
                        <div className="pt-2 border-t border-zinc-800">
                          <div className="grid grid-cols-2 gap-4 text-sm">
                            <div>
                              <p className="text-zinc-400">Events Created</p>
                              <p className="text-white font-medium">{user.eventsCreated}</p>
                            </div>
                            <div>
                              <p className="text-zinc-400">Total Revenue</p>
                              <p className="text-white font-medium">{user.totalRevenue}</p>
                            </div>
                          </div>
                        </div>
                      )}

                      {user.role === "admin" && (
                        <div className="pt-2 border-t border-zinc-800">
                          <div className="text-sm">
                            <p className="text-zinc-400">Permissions</p>
                            <p className="text-white font-medium">{user.permissions}</p>
                          </div>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </AdminLayout>
  )
}
