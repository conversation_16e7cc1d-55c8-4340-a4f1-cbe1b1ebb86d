const InterestedService = require('../services/interestedService');

class InterestedController {
  constructor() {
    this.interestedService = InterestedService.getInstance();
  }

  // Get user's interested events
  getUserInterestedEvents = async (req, res) => {
    try {
      const { accountId, roleType } = req.user;

      // Ensure user is a regular user (not admin or organizer)
      if (roleType !== 'user') {
        return res.status(403).json({
          success: false,
          message: 'Access denied. Only users can view interested events.'
        });
      }

      if (!accountId) {
        return res.status(401).json({
          success: false,
          message: 'User authentication required',
          data: null
        });
      }

      const result = await this.interestedService.getUserInterestedEvents(accountId);

      res.status(200).json({
        success: true,
        message: 'Interested events fetched successfully',
        data: result
      });
    } catch (error) {
      console.error('Error fetching interested events:', error);
      res.status(500).json({
        success: false,
        message: error.message || 'Failed to fetch interested events',
        data: null
      });
    }
  };

  // Add event to interested list
  addToInterested = async (req, res) => {
    try {
      const { accountId, roleType } = req.user;
      const { eventId } = req.body;

      // Ensure user is a regular user (not admin or organizer)
      if (roleType !== 'user') {
        return res.status(403).json({
          success: false,
          message: 'Access denied. Only users can add events to interested list.'
        });
      }

      if (!accountId) {
        return res.status(401).json({
          success: false,
          message: 'User authentication required',
          data: null
        });
      }

      if (!eventId) {
        return res.status(400).json({
          success: false,
          message: 'Event ID is required',
          data: null
        });
      }

      const result = await this.interestedService.addToInterested(accountId, eventId);

      res.status(201).json({
        success: true,
        message: result.message,
        data: {
          event: result.event
        }
      });
    } catch (error) {
      console.error('Error adding event to interested:', error);
      
      // Handle specific error cases
      if (error.message.includes('already in your interested list')) {
        return res.status(409).json({
          success: false,
          message: error.message,
          data: null
        });
      }

      if (error.message.includes('Event not found')) {
        return res.status(404).json({
          success: false,
          message: error.message,
          data: null
        });
      }

      res.status(500).json({
        success: false,
        message: error.message || 'Failed to add event to interested list',
        data: null
      });
    }
  };

  // Remove event from interested list
  removeFromInterested = async (req, res) => {
    try {
      const { accountId, roleType } = req.user;
      const { eventId } = req.params;

      // Ensure user is a regular user (not admin or organizer)
      if (roleType !== 'user') {
        return res.status(403).json({
          success: false,
          message: 'Access denied. Only users can remove events from interested list.'
        });
      }

      if (!accountId) {
        return res.status(401).json({
          success: false,
          message: 'User authentication required',
          data: null
        });
      }

      if (!eventId) {
        return res.status(400).json({
          success: false,
          message: 'Event ID is required',
          data: null
        });
      }

      const result = await this.interestedService.removeFromInterested(accountId, eventId);

      res.status(200).json({
        success: true,
        message: result.message,
        data: null
      });
    } catch (error) {
      console.error('Error removing event from interested:', error);
      
      // Handle specific error cases
      if (error.message.includes('not in your interested list')) {
        return res.status(404).json({
          success: false,
          message: error.message,
          data: null
        });
      }

      res.status(500).json({
        success: false,
        message: error.message || 'Failed to remove event from interested list',
        data: null
      });
    }
  };

  // Check if event is in user's interested list
  checkInterestedStatus = async (req, res) => {
    try {
      const { accountId, roleType } = req.user;
      const { eventId } = req.params;

      // Ensure user is a regular user (not admin or organizer)
      if (roleType !== 'user') {
        return res.status(403).json({
          success: false,
          message: 'Access denied. Only users can check interested status.'
        });
      }

      if (!accountId) {
        return res.status(401).json({
          success: false,
          message: 'User authentication required',
          data: null
        });
      }

      if (!eventId) {
        return res.status(400).json({
          success: false,
          message: 'Event ID is required',
          data: null
        });
      }

      const isInterested = await this.interestedService.isEventInInterested(accountId, eventId);

      res.status(200).json({
        success: true,
        message: 'Interested status checked successfully',
        data: {
          isInterested
        }
      });
    } catch (error) {
      console.error('Error checking interested status:', error);
      res.status(500).json({
        success: false,
        message: error.message || 'Failed to check interested status',
        data: null
      });
    }
  };
}

module.exports = InterestedController;
