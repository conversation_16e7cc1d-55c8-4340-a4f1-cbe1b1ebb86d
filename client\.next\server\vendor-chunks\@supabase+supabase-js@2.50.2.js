"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@supabase+supabase-js@2.50.2";
exports.ids = ["vendor-chunks/@supabase+supabase-js@2.50.2"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.50.2/node_modules/@supabase/supabase-js/dist/module/SupabaseClient.js":
/*!**************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+supabase-js@2.50.2/node_modules/@supabase/supabase-js/dist/module/SupabaseClient.js ***!
  \**************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SupabaseClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_functions_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @supabase/functions-js */ \"(ssr)/./node_modules/.pnpm/@supabase+functions-js@2.4.4/node_modules/@supabase/functions-js/dist/module/FunctionsClient.js\");\n/* harmony import */ var _supabase_postgrest_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/postgrest-js */ \"(ssr)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/esm/wrapper.mjs\");\n/* harmony import */ var _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @supabase/realtime-js */ \"(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/index.js\");\n/* harmony import */ var _supabase_storage_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @supabase/storage-js */ \"(ssr)/./node_modules/.pnpm/@supabase+storage-js@2.7.1/node_modules/@supabase/storage-js/dist/module/StorageClient.js\");\n/* harmony import */ var _lib_constants__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./lib/constants */ \"(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.50.2/node_modules/@supabase/supabase-js/dist/module/lib/constants.js\");\n/* harmony import */ var _lib_fetch__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./lib/fetch */ \"(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.50.2/node_modules/@supabase/supabase-js/dist/module/lib/fetch.js\");\n/* harmony import */ var _lib_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lib/helpers */ \"(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.50.2/node_modules/@supabase/supabase-js/dist/module/lib/helpers.js\");\n/* harmony import */ var _lib_SupabaseAuthClient__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./lib/SupabaseAuthClient */ \"(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.50.2/node_modules/@supabase/supabase-js/dist/module/lib/SupabaseAuthClient.js\");\nvar __awaiter = (undefined && undefined.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\n\n\n\n\n\n\n\n\n/**\n * Supabase Client.\n *\n * An isomorphic Javascript client for interacting with Postgres.\n */\nclass SupabaseClient {\n    /**\n     * Create a new client for use in the browser.\n     * @param supabaseUrl The unique Supabase URL which is supplied when you create a new project in your project dashboard.\n     * @param supabaseKey The unique Supabase Key which is supplied when you create a new project in your project dashboard.\n     * @param options.db.schema You can switch in between schemas. The schema needs to be on the list of exposed schemas inside Supabase.\n     * @param options.auth.autoRefreshToken Set to \"true\" if you want to automatically refresh the token before expiring.\n     * @param options.auth.persistSession Set to \"true\" if you want to automatically save the user session into local storage.\n     * @param options.auth.detectSessionInUrl Set to \"true\" if you want to automatically detects OAuth grants in the URL and signs in the user.\n     * @param options.realtime Options passed along to realtime-js constructor.\n     * @param options.global.fetch A custom fetch implementation.\n     * @param options.global.headers Any additional headers to send with each network request.\n     */\n    constructor(supabaseUrl, supabaseKey, options) {\n        var _a, _b, _c;\n        this.supabaseUrl = supabaseUrl;\n        this.supabaseKey = supabaseKey;\n        if (!supabaseUrl)\n            throw new Error('supabaseUrl is required.');\n        if (!supabaseKey)\n            throw new Error('supabaseKey is required.');\n        const _supabaseUrl = (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_2__.ensureTrailingSlash)(supabaseUrl);\n        const baseUrl = new URL(_supabaseUrl);\n        this.realtimeUrl = new URL('realtime/v1', baseUrl);\n        this.realtimeUrl.protocol = this.realtimeUrl.protocol.replace('http', 'ws');\n        this.authUrl = new URL('auth/v1', baseUrl);\n        this.storageUrl = new URL('storage/v1', baseUrl);\n        this.functionsUrl = new URL('functions/v1', baseUrl);\n        // default storage key uses the supabase project ref as a namespace\n        const defaultStorageKey = `sb-${baseUrl.hostname.split('.')[0]}-auth-token`;\n        const DEFAULTS = {\n            db: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.DEFAULT_DB_OPTIONS,\n            realtime: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.DEFAULT_REALTIME_OPTIONS,\n            auth: Object.assign(Object.assign({}, _lib_constants__WEBPACK_IMPORTED_MODULE_3__.DEFAULT_AUTH_OPTIONS), { storageKey: defaultStorageKey }),\n            global: _lib_constants__WEBPACK_IMPORTED_MODULE_3__.DEFAULT_GLOBAL_OPTIONS,\n        };\n        const settings = (0,_lib_helpers__WEBPACK_IMPORTED_MODULE_2__.applySettingDefaults)(options !== null && options !== void 0 ? options : {}, DEFAULTS);\n        this.storageKey = (_a = settings.auth.storageKey) !== null && _a !== void 0 ? _a : '';\n        this.headers = (_b = settings.global.headers) !== null && _b !== void 0 ? _b : {};\n        if (!settings.accessToken) {\n            this.auth = this._initSupabaseAuthClient((_c = settings.auth) !== null && _c !== void 0 ? _c : {}, this.headers, settings.global.fetch);\n        }\n        else {\n            this.accessToken = settings.accessToken;\n            this.auth = new Proxy({}, {\n                get: (_, prop) => {\n                    throw new Error(`@supabase/supabase-js: Supabase Client is configured with the accessToken option, accessing supabase.auth.${String(prop)} is not possible`);\n                },\n            });\n        }\n        this.fetch = (0,_lib_fetch__WEBPACK_IMPORTED_MODULE_4__.fetchWithAuth)(supabaseKey, this._getAccessToken.bind(this), settings.global.fetch);\n        this.realtime = this._initRealtimeClient(Object.assign({ headers: this.headers, accessToken: this._getAccessToken.bind(this) }, settings.realtime));\n        this.rest = new _supabase_postgrest_js__WEBPACK_IMPORTED_MODULE_0__.PostgrestClient(new URL('rest/v1', baseUrl).href, {\n            headers: this.headers,\n            schema: settings.db.schema,\n            fetch: this.fetch,\n        });\n        if (!settings.accessToken) {\n            this._listenForAuthEvents();\n        }\n    }\n    /**\n     * Supabase Functions allows you to deploy and invoke edge functions.\n     */\n    get functions() {\n        return new _supabase_functions_js__WEBPACK_IMPORTED_MODULE_5__.FunctionsClient(this.functionsUrl.href, {\n            headers: this.headers,\n            customFetch: this.fetch,\n        });\n    }\n    /**\n     * Supabase Storage allows you to manage user-generated content, such as photos or videos.\n     */\n    get storage() {\n        return new _supabase_storage_js__WEBPACK_IMPORTED_MODULE_6__.StorageClient(this.storageUrl.href, this.headers, this.fetch);\n    }\n    /**\n     * Perform a query on a table or a view.\n     *\n     * @param relation - The table or view name to query\n     */\n    from(relation) {\n        return this.rest.from(relation);\n    }\n    // NOTE: signatures must be kept in sync with PostgrestClient.schema\n    /**\n     * Select a schema to query or perform an function (rpc) call.\n     *\n     * The schema needs to be on the list of exposed schemas inside Supabase.\n     *\n     * @param schema - The schema to query\n     */\n    schema(schema) {\n        return this.rest.schema(schema);\n    }\n    // NOTE: signatures must be kept in sync with PostgrestClient.rpc\n    /**\n     * Perform a function call.\n     *\n     * @param fn - The function name to call\n     * @param args - The arguments to pass to the function call\n     * @param options - Named parameters\n     * @param options.head - When set to `true`, `data` will not be returned.\n     * Useful if you only need the count.\n     * @param options.get - When set to `true`, the function will be called with\n     * read-only access mode.\n     * @param options.count - Count algorithm to use to count rows returned by the\n     * function. Only applicable for [set-returning\n     * functions](https://www.postgresql.org/docs/current/functions-srf.html).\n     *\n     * `\"exact\"`: Exact but slow count algorithm. Performs a `COUNT(*)` under the\n     * hood.\n     *\n     * `\"planned\"`: Approximated but fast count algorithm. Uses the Postgres\n     * statistics under the hood.\n     *\n     * `\"estimated\"`: Uses exact count for low numbers and planned count for high\n     * numbers.\n     */\n    rpc(fn, args = {}, options = {}) {\n        return this.rest.rpc(fn, args, options);\n    }\n    /**\n     * Creates a Realtime channel with Broadcast, Presence, and Postgres Changes.\n     *\n     * @param {string} name - The name of the Realtime channel.\n     * @param {Object} opts - The options to pass to the Realtime channel.\n     *\n     */\n    channel(name, opts = { config: {} }) {\n        return this.realtime.channel(name, opts);\n    }\n    /**\n     * Returns all Realtime channels.\n     */\n    getChannels() {\n        return this.realtime.getChannels();\n    }\n    /**\n     * Unsubscribes and removes Realtime channel from Realtime client.\n     *\n     * @param {RealtimeChannel} channel - The name of the Realtime channel.\n     *\n     */\n    removeChannel(channel) {\n        return this.realtime.removeChannel(channel);\n    }\n    /**\n     * Unsubscribes and removes all Realtime channels from Realtime client.\n     */\n    removeAllChannels() {\n        return this.realtime.removeAllChannels();\n    }\n    _getAccessToken() {\n        var _a, _b;\n        return __awaiter(this, void 0, void 0, function* () {\n            if (this.accessToken) {\n                return yield this.accessToken();\n            }\n            const { data } = yield this.auth.getSession();\n            return (_b = (_a = data.session) === null || _a === void 0 ? void 0 : _a.access_token) !== null && _b !== void 0 ? _b : null;\n        });\n    }\n    _initSupabaseAuthClient({ autoRefreshToken, persistSession, detectSessionInUrl, storage, storageKey, flowType, lock, debug, }, headers, fetch) {\n        const authHeaders = {\n            Authorization: `Bearer ${this.supabaseKey}`,\n            apikey: `${this.supabaseKey}`,\n        };\n        return new _lib_SupabaseAuthClient__WEBPACK_IMPORTED_MODULE_7__.SupabaseAuthClient({\n            url: this.authUrl.href,\n            headers: Object.assign(Object.assign({}, authHeaders), headers),\n            storageKey: storageKey,\n            autoRefreshToken,\n            persistSession,\n            detectSessionInUrl,\n            storage,\n            flowType,\n            lock,\n            debug,\n            fetch,\n            // auth checks if there is a custom authorizaiton header using this flag\n            // so it knows whether to return an error when getUser is called with no session\n            hasCustomAuthorizationHeader: 'Authorization' in this.headers,\n        });\n    }\n    _initRealtimeClient(options) {\n        return new _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_1__.RealtimeClient(this.realtimeUrl.href, Object.assign(Object.assign({}, options), { params: Object.assign({ apikey: this.supabaseKey }, options === null || options === void 0 ? void 0 : options.params) }));\n    }\n    _listenForAuthEvents() {\n        let data = this.auth.onAuthStateChange((event, session) => {\n            this._handleTokenChanged(event, 'CLIENT', session === null || session === void 0 ? void 0 : session.access_token);\n        });\n        return data;\n    }\n    _handleTokenChanged(event, source, token) {\n        if ((event === 'TOKEN_REFRESHED' || event === 'SIGNED_IN') &&\n            this.changedAccessToken !== token) {\n            this.changedAccessToken = token;\n        }\n        else if (event === 'SIGNED_OUT') {\n            this.realtime.setAuth();\n            if (source == 'STORAGE')\n                this.auth.signOut();\n            this.changedAccessToken = undefined;\n        }\n    }\n}\n//# sourceMappingURL=SupabaseClient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.50.2/node_modules/@supabase/supabase-js/dist/module/SupabaseClient.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.50.2/node_modules/@supabase/supabase-js/dist/module/index.js":
/*!*****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+supabase-js@2.50.2/node_modules/@supabase/supabase-js/dist/module/index.js ***!
  \*****************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthAdminApi: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthAdminApi),\n/* harmony export */   AuthApiError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthApiError),\n/* harmony export */   AuthClient: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthClient),\n/* harmony export */   AuthError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthError),\n/* harmony export */   AuthImplicitGrantRedirectError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthImplicitGrantRedirectError),\n/* harmony export */   AuthInvalidCredentialsError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthInvalidCredentialsError),\n/* harmony export */   AuthInvalidJwtError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthInvalidJwtError),\n/* harmony export */   AuthInvalidTokenResponseError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthInvalidTokenResponseError),\n/* harmony export */   AuthPKCEGrantCodeExchangeError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthPKCEGrantCodeExchangeError),\n/* harmony export */   AuthRetryableFetchError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthRetryableFetchError),\n/* harmony export */   AuthSessionMissingError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthSessionMissingError),\n/* harmony export */   AuthUnknownError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthUnknownError),\n/* harmony export */   AuthWeakPasswordError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthWeakPasswordError),\n/* harmony export */   CustomAuthError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.CustomAuthError),\n/* harmony export */   FunctionRegion: () => (/* reexport safe */ _supabase_functions_js__WEBPACK_IMPORTED_MODULE_2__.FunctionRegion),\n/* harmony export */   FunctionsError: () => (/* reexport safe */ _supabase_functions_js__WEBPACK_IMPORTED_MODULE_2__.FunctionsError),\n/* harmony export */   FunctionsFetchError: () => (/* reexport safe */ _supabase_functions_js__WEBPACK_IMPORTED_MODULE_2__.FunctionsFetchError),\n/* harmony export */   FunctionsHttpError: () => (/* reexport safe */ _supabase_functions_js__WEBPACK_IMPORTED_MODULE_2__.FunctionsHttpError),\n/* harmony export */   FunctionsRelayError: () => (/* reexport safe */ _supabase_functions_js__WEBPACK_IMPORTED_MODULE_2__.FunctionsRelayError),\n/* harmony export */   GoTrueAdminApi: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.GoTrueAdminApi),\n/* harmony export */   GoTrueClient: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.GoTrueClient),\n/* harmony export */   NavigatorLockAcquireTimeoutError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.NavigatorLockAcquireTimeoutError),\n/* harmony export */   PostgrestError: () => (/* reexport safe */ _supabase_postgrest_js__WEBPACK_IMPORTED_MODULE_1__.PostgrestError),\n/* harmony export */   REALTIME_CHANNEL_STATES: () => (/* reexport safe */ _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_3__.REALTIME_CHANNEL_STATES),\n/* harmony export */   REALTIME_LISTEN_TYPES: () => (/* reexport safe */ _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_3__.REALTIME_LISTEN_TYPES),\n/* harmony export */   REALTIME_POSTGRES_CHANGES_LISTEN_EVENT: () => (/* reexport safe */ _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_3__.REALTIME_POSTGRES_CHANGES_LISTEN_EVENT),\n/* harmony export */   REALTIME_PRESENCE_LISTEN_EVENTS: () => (/* reexport safe */ _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_3__.REALTIME_PRESENCE_LISTEN_EVENTS),\n/* harmony export */   REALTIME_SUBSCRIBE_STATES: () => (/* reexport safe */ _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_3__.REALTIME_SUBSCRIBE_STATES),\n/* harmony export */   RealtimeChannel: () => (/* reexport safe */ _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_3__.RealtimeChannel),\n/* harmony export */   RealtimeClient: () => (/* reexport safe */ _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_3__.RealtimeClient),\n/* harmony export */   RealtimePresence: () => (/* reexport safe */ _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_3__.RealtimePresence),\n/* harmony export */   SIGN_OUT_SCOPES: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.SIGN_OUT_SCOPES),\n/* harmony export */   SupabaseClient: () => (/* reexport safe */ _SupabaseClient__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   createClient: () => (/* binding */ createClient),\n/* harmony export */   isAuthApiError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.isAuthApiError),\n/* harmony export */   isAuthError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.isAuthError),\n/* harmony export */   isAuthImplicitGrantRedirectError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.isAuthImplicitGrantRedirectError),\n/* harmony export */   isAuthRetryableFetchError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.isAuthRetryableFetchError),\n/* harmony export */   isAuthSessionMissingError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.isAuthSessionMissingError),\n/* harmony export */   isAuthWeakPasswordError: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.isAuthWeakPasswordError),\n/* harmony export */   lockInternals: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.lockInternals),\n/* harmony export */   navigatorLock: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.navigatorLock),\n/* harmony export */   processLock: () => (/* reexport safe */ _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.processLock)\n/* harmony export */ });\n/* harmony import */ var _SupabaseClient__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./SupabaseClient */ \"(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.50.2/node_modules/@supabase/supabase-js/dist/module/SupabaseClient.js\");\n/* harmony import */ var _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/auth-js */ \"(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/index.js\");\n/* harmony import */ var _supabase_postgrest_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @supabase/postgrest-js */ \"(ssr)/./node_modules/.pnpm/@supabase+postgrest-js@1.19.4/node_modules/@supabase/postgrest-js/dist/esm/wrapper.mjs\");\n/* harmony import */ var _supabase_functions_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @supabase/functions-js */ \"(ssr)/./node_modules/.pnpm/@supabase+functions-js@2.4.4/node_modules/@supabase/functions-js/dist/module/types.js\");\n/* harmony import */ var _supabase_realtime_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @supabase/realtime-js */ \"(ssr)/./node_modules/.pnpm/@supabase+realtime-js@2.11.15/node_modules/@supabase/realtime-js/dist/module/index.js\");\n\n\n\n\n\n\n/**\n * Creates a new Supabase Client.\n */\nconst createClient = (supabaseUrl, supabaseKey, options) => {\n    return new _SupabaseClient__WEBPACK_IMPORTED_MODULE_4__[\"default\"](supabaseUrl, supabaseKey, options);\n};\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHN1cGFiYXNlK3N1cGFiYXNlLWpzQDIuNTAuMi9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL3N1cGFiYXNlLWpzL2Rpc3QvbW9kdWxlL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUE4QztBQUNaO0FBQ3VCO0FBQzhFO0FBQ2pHO0FBQ3VCO0FBQzdEO0FBQ0E7QUFDQTtBQUNPO0FBQ1AsZUFBZSx1REFBYztBQUM3QjtBQUNBIiwic291cmNlcyI6WyJEOlxcUHJvamVjdCBmb3IgQ2xpZW50c1xcQ291bnRlckJEXFxDb3VudGVyc0JEXFxjbGllbnRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBzdXBhYmFzZStzdXBhYmFzZS1qc0AyLjUwLjJcXG5vZGVfbW9kdWxlc1xcQHN1cGFiYXNlXFxzdXBhYmFzZS1qc1xcZGlzdFxcbW9kdWxlXFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgU3VwYWJhc2VDbGllbnQgZnJvbSAnLi9TdXBhYmFzZUNsaWVudCc7XG5leHBvcnQgKiBmcm9tICdAc3VwYWJhc2UvYXV0aC1qcyc7XG5leHBvcnQgeyBQb3N0Z3Jlc3RFcnJvciwgfSBmcm9tICdAc3VwYWJhc2UvcG9zdGdyZXN0LWpzJztcbmV4cG9ydCB7IEZ1bmN0aW9uc0h0dHBFcnJvciwgRnVuY3Rpb25zRmV0Y2hFcnJvciwgRnVuY3Rpb25zUmVsYXlFcnJvciwgRnVuY3Rpb25zRXJyb3IsIEZ1bmN0aW9uUmVnaW9uLCB9IGZyb20gJ0BzdXBhYmFzZS9mdW5jdGlvbnMtanMnO1xuZXhwb3J0ICogZnJvbSAnQHN1cGFiYXNlL3JlYWx0aW1lLWpzJztcbmV4cG9ydCB7IGRlZmF1bHQgYXMgU3VwYWJhc2VDbGllbnQgfSBmcm9tICcuL1N1cGFiYXNlQ2xpZW50Jztcbi8qKlxuICogQ3JlYXRlcyBhIG5ldyBTdXBhYmFzZSBDbGllbnQuXG4gKi9cbmV4cG9ydCBjb25zdCBjcmVhdGVDbGllbnQgPSAoc3VwYWJhc2VVcmwsIHN1cGFiYXNlS2V5LCBvcHRpb25zKSA9PiB7XG4gICAgcmV0dXJuIG5ldyBTdXBhYmFzZUNsaWVudChzdXBhYmFzZVVybCwgc3VwYWJhc2VLZXksIG9wdGlvbnMpO1xufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4LmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.50.2/node_modules/@supabase/supabase-js/dist/module/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.50.2/node_modules/@supabase/supabase-js/dist/module/lib/SupabaseAuthClient.js":
/*!**********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+supabase-js@2.50.2/node_modules/@supabase/supabase-js/dist/module/lib/SupabaseAuthClient.js ***!
  \**********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SupabaseAuthClient: () => (/* binding */ SupabaseAuthClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/auth-js */ \"(ssr)/./node_modules/.pnpm/@supabase+auth-js@2.70.0/node_modules/@supabase/auth-js/dist/module/index.js\");\n\nclass SupabaseAuthClient extends _supabase_auth_js__WEBPACK_IMPORTED_MODULE_0__.AuthClient {\n    constructor(options) {\n        super(options);\n    }\n}\n//# sourceMappingURL=SupabaseAuthClient.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHN1cGFiYXNlK3N1cGFiYXNlLWpzQDIuNTAuMi9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL3N1cGFiYXNlLWpzL2Rpc3QvbW9kdWxlL2xpYi9TdXBhYmFzZUF1dGhDbGllbnQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBK0M7QUFDeEMsaUNBQWlDLHlEQUFVO0FBQ2xEO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkQ6XFxQcm9qZWN0IGZvciBDbGllbnRzXFxDb3VudGVyQkRcXENvdW50ZXJzQkRcXGNsaWVudFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQHN1cGFiYXNlK3N1cGFiYXNlLWpzQDIuNTAuMlxcbm9kZV9tb2R1bGVzXFxAc3VwYWJhc2VcXHN1cGFiYXNlLWpzXFxkaXN0XFxtb2R1bGVcXGxpYlxcU3VwYWJhc2VBdXRoQ2xpZW50LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEF1dGhDbGllbnQgfSBmcm9tICdAc3VwYWJhc2UvYXV0aC1qcyc7XG5leHBvcnQgY2xhc3MgU3VwYWJhc2VBdXRoQ2xpZW50IGV4dGVuZHMgQXV0aENsaWVudCB7XG4gICAgY29uc3RydWN0b3Iob3B0aW9ucykge1xuICAgICAgICBzdXBlcihvcHRpb25zKTtcbiAgICB9XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1TdXBhYmFzZUF1dGhDbGllbnQuanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.50.2/node_modules/@supabase/supabase-js/dist/module/lib/SupabaseAuthClient.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.50.2/node_modules/@supabase/supabase-js/dist/module/lib/constants.js":
/*!*************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+supabase-js@2.50.2/node_modules/@supabase/supabase-js/dist/module/lib/constants.js ***!
  \*************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_AUTH_OPTIONS: () => (/* binding */ DEFAULT_AUTH_OPTIONS),\n/* harmony export */   DEFAULT_DB_OPTIONS: () => (/* binding */ DEFAULT_DB_OPTIONS),\n/* harmony export */   DEFAULT_GLOBAL_OPTIONS: () => (/* binding */ DEFAULT_GLOBAL_OPTIONS),\n/* harmony export */   DEFAULT_HEADERS: () => (/* binding */ DEFAULT_HEADERS),\n/* harmony export */   DEFAULT_REALTIME_OPTIONS: () => (/* binding */ DEFAULT_REALTIME_OPTIONS)\n/* harmony export */ });\n/* harmony import */ var _version__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./version */ \"(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.50.2/node_modules/@supabase/supabase-js/dist/module/lib/version.js\");\n\nlet JS_ENV = '';\n// @ts-ignore\nif (typeof Deno !== 'undefined') {\n    JS_ENV = 'deno';\n}\nelse if (typeof document !== 'undefined') {\n    JS_ENV = 'web';\n}\nelse if (typeof navigator !== 'undefined' && navigator.product === 'ReactNative') {\n    JS_ENV = 'react-native';\n}\nelse {\n    JS_ENV = 'node';\n}\nconst DEFAULT_HEADERS = { 'X-Client-Info': `supabase-js-${JS_ENV}/${_version__WEBPACK_IMPORTED_MODULE_0__.version}` };\nconst DEFAULT_GLOBAL_OPTIONS = {\n    headers: DEFAULT_HEADERS,\n};\nconst DEFAULT_DB_OPTIONS = {\n    schema: 'public',\n};\nconst DEFAULT_AUTH_OPTIONS = {\n    autoRefreshToken: true,\n    persistSession: true,\n    detectSessionInUrl: true,\n    flowType: 'implicit',\n};\nconst DEFAULT_REALTIME_OPTIONS = {};\n//# sourceMappingURL=constants.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHN1cGFiYXNlK3N1cGFiYXNlLWpzQDIuNTAuMi9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL3N1cGFiYXNlLWpzL2Rpc3QvbW9kdWxlL2xpYi9jb25zdGFudHMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQW9DO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTywwQkFBMEIsZ0NBQWdDLE9BQU8sR0FBRyw2Q0FBTyxDQUFDO0FBQzVFO0FBQ1A7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1AiLCJzb3VyY2VzIjpbIkQ6XFxQcm9qZWN0IGZvciBDbGllbnRzXFxDb3VudGVyQkRcXENvdW50ZXJzQkRcXGNsaWVudFxcbm9kZV9tb2R1bGVzXFwucG5wbVxcQHN1cGFiYXNlK3N1cGFiYXNlLWpzQDIuNTAuMlxcbm9kZV9tb2R1bGVzXFxAc3VwYWJhc2VcXHN1cGFiYXNlLWpzXFxkaXN0XFxtb2R1bGVcXGxpYlxcY29uc3RhbnRzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHZlcnNpb24gfSBmcm9tICcuL3ZlcnNpb24nO1xubGV0IEpTX0VOViA9ICcnO1xuLy8gQHRzLWlnbm9yZVxuaWYgKHR5cGVvZiBEZW5vICE9PSAndW5kZWZpbmVkJykge1xuICAgIEpTX0VOViA9ICdkZW5vJztcbn1cbmVsc2UgaWYgKHR5cGVvZiBkb2N1bWVudCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICBKU19FTlYgPSAnd2ViJztcbn1cbmVsc2UgaWYgKHR5cGVvZiBuYXZpZ2F0b3IgIT09ICd1bmRlZmluZWQnICYmIG5hdmlnYXRvci5wcm9kdWN0ID09PSAnUmVhY3ROYXRpdmUnKSB7XG4gICAgSlNfRU5WID0gJ3JlYWN0LW5hdGl2ZSc7XG59XG5lbHNlIHtcbiAgICBKU19FTlYgPSAnbm9kZSc7XG59XG5leHBvcnQgY29uc3QgREVGQVVMVF9IRUFERVJTID0geyAnWC1DbGllbnQtSW5mbyc6IGBzdXBhYmFzZS1qcy0ke0pTX0VOVn0vJHt2ZXJzaW9ufWAgfTtcbmV4cG9ydCBjb25zdCBERUZBVUxUX0dMT0JBTF9PUFRJT05TID0ge1xuICAgIGhlYWRlcnM6IERFRkFVTFRfSEVBREVSUyxcbn07XG5leHBvcnQgY29uc3QgREVGQVVMVF9EQl9PUFRJT05TID0ge1xuICAgIHNjaGVtYTogJ3B1YmxpYycsXG59O1xuZXhwb3J0IGNvbnN0IERFRkFVTFRfQVVUSF9PUFRJT05TID0ge1xuICAgIGF1dG9SZWZyZXNoVG9rZW46IHRydWUsXG4gICAgcGVyc2lzdFNlc3Npb246IHRydWUsXG4gICAgZGV0ZWN0U2Vzc2lvbkluVXJsOiB0cnVlLFxuICAgIGZsb3dUeXBlOiAnaW1wbGljaXQnLFxufTtcbmV4cG9ydCBjb25zdCBERUZBVUxUX1JFQUxUSU1FX09QVElPTlMgPSB7fTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWNvbnN0YW50cy5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.50.2/node_modules/@supabase/supabase-js/dist/module/lib/constants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.50.2/node_modules/@supabase/supabase-js/dist/module/lib/fetch.js":
/*!*********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+supabase-js@2.50.2/node_modules/@supabase/supabase-js/dist/module/lib/fetch.js ***!
  \*********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fetchWithAuth: () => (/* binding */ fetchWithAuth),\n/* harmony export */   resolveFetch: () => (/* binding */ resolveFetch),\n/* harmony export */   resolveHeadersConstructor: () => (/* binding */ resolveHeadersConstructor)\n/* harmony export */ });\n/* harmony import */ var _supabase_node_fetch__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/node-fetch */ \"(ssr)/./node_modules/.pnpm/@supabase+node-fetch@2.6.15/node_modules/@supabase/node-fetch/lib/index.js\");\n/* harmony import */ var _supabase_node_fetch__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_node_fetch__WEBPACK_IMPORTED_MODULE_0__);\nvar __awaiter = (undefined && undefined.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\n// @ts-ignore\n\nconst resolveFetch = (customFetch) => {\n    let _fetch;\n    if (customFetch) {\n        _fetch = customFetch;\n    }\n    else if (typeof fetch === 'undefined') {\n        _fetch = (_supabase_node_fetch__WEBPACK_IMPORTED_MODULE_0___default());\n    }\n    else {\n        _fetch = fetch;\n    }\n    return (...args) => _fetch(...args);\n};\nconst resolveHeadersConstructor = () => {\n    if (typeof Headers === 'undefined') {\n        return _supabase_node_fetch__WEBPACK_IMPORTED_MODULE_0__.Headers;\n    }\n    return Headers;\n};\nconst fetchWithAuth = (supabaseKey, getAccessToken, customFetch) => {\n    const fetch = resolveFetch(customFetch);\n    const HeadersConstructor = resolveHeadersConstructor();\n    return (input, init) => __awaiter(void 0, void 0, void 0, function* () {\n        var _a;\n        const accessToken = (_a = (yield getAccessToken())) !== null && _a !== void 0 ? _a : supabaseKey;\n        let headers = new HeadersConstructor(init === null || init === void 0 ? void 0 : init.headers);\n        if (!headers.has('apikey')) {\n            headers.set('apikey', supabaseKey);\n        }\n        if (!headers.has('Authorization')) {\n            headers.set('Authorization', `Bearer ${accessToken}`);\n        }\n        return fetch(input, Object.assign(Object.assign({}, init), { headers }));\n    });\n};\n//# sourceMappingURL=fetch.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.50.2/node_modules/@supabase/supabase-js/dist/module/lib/fetch.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.50.2/node_modules/@supabase/supabase-js/dist/module/lib/helpers.js":
/*!***********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+supabase-js@2.50.2/node_modules/@supabase/supabase-js/dist/module/lib/helpers.js ***!
  \***********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   applySettingDefaults: () => (/* binding */ applySettingDefaults),\n/* harmony export */   ensureTrailingSlash: () => (/* binding */ ensureTrailingSlash),\n/* harmony export */   isBrowser: () => (/* binding */ isBrowser),\n/* harmony export */   uuid: () => (/* binding */ uuid)\n/* harmony export */ });\nvar __awaiter = (undefined && undefined.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nfunction uuid() {\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\n        var r = (Math.random() * 16) | 0, v = c == 'x' ? r : (r & 0x3) | 0x8;\n        return v.toString(16);\n    });\n}\nfunction ensureTrailingSlash(url) {\n    return url.endsWith('/') ? url : url + '/';\n}\nconst isBrowser = () => typeof window !== 'undefined';\nfunction applySettingDefaults(options, defaults) {\n    var _a, _b;\n    const { db: dbOptions, auth: authOptions, realtime: realtimeOptions, global: globalOptions, } = options;\n    const { db: DEFAULT_DB_OPTIONS, auth: DEFAULT_AUTH_OPTIONS, realtime: DEFAULT_REALTIME_OPTIONS, global: DEFAULT_GLOBAL_OPTIONS, } = defaults;\n    const result = {\n        db: Object.assign(Object.assign({}, DEFAULT_DB_OPTIONS), dbOptions),\n        auth: Object.assign(Object.assign({}, DEFAULT_AUTH_OPTIONS), authOptions),\n        realtime: Object.assign(Object.assign({}, DEFAULT_REALTIME_OPTIONS), realtimeOptions),\n        global: Object.assign(Object.assign(Object.assign({}, DEFAULT_GLOBAL_OPTIONS), globalOptions), { headers: Object.assign(Object.assign({}, ((_a = DEFAULT_GLOBAL_OPTIONS === null || DEFAULT_GLOBAL_OPTIONS === void 0 ? void 0 : DEFAULT_GLOBAL_OPTIONS.headers) !== null && _a !== void 0 ? _a : {})), ((_b = globalOptions === null || globalOptions === void 0 ? void 0 : globalOptions.headers) !== null && _b !== void 0 ? _b : {})) }),\n        accessToken: () => __awaiter(this, void 0, void 0, function* () { return ''; }),\n    };\n    if (options.accessToken) {\n        result.accessToken = options.accessToken;\n    }\n    else {\n        // hack around Required<>\n        delete result.accessToken;\n    }\n    return result;\n}\n//# sourceMappingURL=helpers.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.50.2/node_modules/@supabase/supabase-js/dist/module/lib/helpers.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.50.2/node_modules/@supabase/supabase-js/dist/module/lib/version.js":
/*!***********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@supabase+supabase-js@2.50.2/node_modules/@supabase/supabase-js/dist/module/lib/version.js ***!
  \***********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   version: () => (/* binding */ version)\n/* harmony export */ });\nconst version = '2.50.2';\n//# sourceMappingURL=version.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHN1cGFiYXNlK3N1cGFiYXNlLWpzQDIuNTAuMi9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL3N1cGFiYXNlLWpzL2Rpc3QvbW9kdWxlL2xpYi92ZXJzaW9uLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQIiwic291cmNlcyI6WyJEOlxcUHJvamVjdCBmb3IgQ2xpZW50c1xcQ291bnRlckJEXFxDb3VudGVyc0JEXFxjbGllbnRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBzdXBhYmFzZStzdXBhYmFzZS1qc0AyLjUwLjJcXG5vZGVfbW9kdWxlc1xcQHN1cGFiYXNlXFxzdXBhYmFzZS1qc1xcZGlzdFxcbW9kdWxlXFxsaWJcXHZlcnNpb24uanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IHZlcnNpb24gPSAnMi41MC4yJztcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXZlcnNpb24uanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@supabase+supabase-js@2.50.2/node_modules/@supabase/supabase-js/dist/module/lib/version.js\n");

/***/ })

};
;