const EventService = require('../services/eventService');

class EventController {
  constructor() {
    this.eventService = new EventService();
  }

  // Get all events (no server-side filtering)
  getAllEvents = async (req, res) => {
    try {
      const result = await this.eventService.getAllEvents();

      res.status(200).json({
        success: true,
        message: 'Events fetched successfully',
        data: result
      });
    } catch (error) {
      console.error('Error fetching events:', error);
      res.status(500).json({
        success: false,
        message: error.message || 'Failed to fetch events',
        data: null
      });
    }
  };

  // Get event by ID
  getEventById = async (req, res) => {
    try {
      const { id } = req.params;

      if (!id || isNaN(parseInt(id))) {
        return res.status(400).json({
          success: false,
          message: 'Valid event ID is required',
          data: null
        });
      }

      const event = await this.eventService.getEventById(id);

      res.status(200).json({
        success: true,
        message: 'Event fetched successfully',
        data: event
      });
    } catch (error) {
      console.error('Error fetching event:', error);

      if (error.message === 'Event not found') {
        return res.status(404).json({
          success: false,
          message: 'Event not found',
          data: null
        });
      }

      res.status(500).json({
        success: false,
        message: error.message || 'Failed to fetch event',
        data: null
      });
    }
  };

  // Get events by organizer
  getEventsByOrganizer = async (req, res) => {
    try {
      const { organizerId } = req.params;
      const filters = {
        page: req.query.page || 1,
        limit: req.query.limit || 12
      };

      if (!organizerId || isNaN(parseInt(organizerId))) {
        return res.status(400).json({
          success: false,
          message: 'Valid organizer ID is required',
          data: null
        });
      }

      const result = await this.eventService.getEventsByOrganizer(organizerId, filters);

      res.status(200).json({
        success: true,
        message: 'Organizer events fetched successfully',
        data: result
      });
    } catch (error) {
      console.error('Error fetching organizer events:', error);
      res.status(500).json({
        success: false,
        message: error.message || 'Failed to fetch organizer events',
        data: null
      });
    }
  };

  // Get all genres
  getAllGenres = async (req, res) => {
    try {
      const genres = await this.eventService.getAllGenres();

      res.status(200).json({
        success: true,
        message: 'Genres fetched successfully',
        data: genres
      });
    } catch (error) {
      console.error('Error fetching genres:', error);
      res.status(500).json({
        success: false,
        message: error.message || 'Failed to fetch genres',
        data: null
      });
    }
  };

  // Get all locations
  getAllLocations = async (req, res) => {
    try {
      const locations = await this.eventService.getAllLocations();

      res.status(200).json({
        success: true,
        message: 'Locations fetched successfully',
        data: locations
      });
    } catch (error) {
      console.error('Error fetching locations:', error);
      res.status(500).json({
        success: false,
        message: error.message || 'Failed to fetch locations',
        data: null
      });
    }
  };




}

module.exports = EventController;
