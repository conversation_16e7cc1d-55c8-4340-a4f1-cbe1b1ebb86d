"use client"

import { useEffect } from "react"
import { useRouter } from "next/navigation"
import { motion } from "framer-motion"
import DashboardLayout from "@/components/dashboard/dashboard-layout"
import { useAuth } from "@/context/auth-context"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { DollarSign, Ticket, Calendar, Users, TrendingUp, TrendingDown, Plus, QrCode } from "lucide-react"

// Mock data for dashboard
const mockData = {
  totalRevenue: 125430,
  totalTicketsSold: 2847,
  activeEvents: 12,
  totalAttendees: 2654,
  monthlyRevenue: [
    { month: "Jan", revenue: 12000, tickets: 240 },
    { month: "Feb", revenue: 15000, tickets: 300 },
    { month: "Mar", revenue: 18000, tickets: 360 },
    { month: "Apr", revenue: 22000, tickets: 440 },
    { month: "May", revenue: 25000, tickets: 500 },
    { month: "Jun", revenue: 33430, tickets: 667 },
  ],
  recentEvents: [
    { id: 1, name: "Summer Music Festival", revenue: 45000, tickets: 900, status: "active" },
    { id: 2, name: "Tech Conference 2024", revenue: 32000, tickets: 640, status: "active" },
    { id: 3, name: "Food & Wine Expo", revenue: 18000, tickets: 360, status: "completed" },
  ],
}

const StatCard = ({ title, value, icon: Icon, change, changeType }) => (
  <Card className="bg-zinc-900 border-zinc-800">
    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
      <CardTitle className="text-sm font-medium text-zinc-400">{title}</CardTitle>
      <Icon className="h-4 w-4 text-zinc-400" />
    </CardHeader>
    <CardContent>
      <div className="text-2xl font-bold text-white">{value}</div>
      {change && (
        <div
          className={`flex items-center text-xs mt-1 ${changeType === "positive" ? "text-green-500" : "text-red-500"}`}
        >
          {changeType === "positive" ? (
            <TrendingUp className="h-3 w-3 mr-1" />
          ) : (
            <TrendingDown className="h-3 w-3 mr-1" />
          )}
          {change}% from last month
        </div>
      )}
    </CardContent>
  </Card>
)

const SimpleChart = ({ data }) => {
  const maxRevenue = Math.max(...data.map((d) => d.revenue))

  return (
    <div className="space-y-4">
      {data.map((item, index) => (
        <div key={item.month} className="flex items-center gap-4">
          <div className="w-8 text-sm text-zinc-400">{item.month}</div>
          <div className="flex-1">
            <div className="flex items-center justify-between mb-1">
              <span className="text-sm text-zinc-300">${item.revenue.toLocaleString()}</span>
              <span className="text-xs text-zinc-500">{item.tickets} tickets</span>
            </div>
            <div className="w-full bg-zinc-800 rounded-full h-2">
              <motion.div
                className="bg-gradient-to-r from-red-600 to-red-500 h-2 rounded-full"
                initial={{ width: 0 }}
                animate={{ width: `${(item.revenue / maxRevenue) * 100}%` }}
                transition={{ duration: 1, delay: index * 0.1 }}
              />
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}

export default function DashboardPage() {
  const router = useRouter()
  const { user } = useAuth()

  useEffect(() => {
    if (!user) {
      router.push("/")
      return
    }
  }, [user, router])

  if (!user) {
    return null
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <StatCard
            title="Total Revenue"
            value={`$${mockData.totalRevenue.toLocaleString()}`}
            icon={DollarSign}
            change={12.5}
            changeType="positive"
          />
          <StatCard
            title="Tickets Sold"
            value={mockData.totalTicketsSold.toLocaleString()}
            icon={Ticket}
            change={8.2}
            changeType="positive"
          />
          <StatCard
            title="Active Events"
            value={mockData.activeEvents}
            icon={Calendar}
            change={-2.1}
            changeType="negative"
          />
          <StatCard
            title="Total Attendees"
            value={mockData.totalAttendees.toLocaleString()}
            icon={Users}
            change={15.3}
            changeType="positive"
          />
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Revenue Chart */}
          <Card className="bg-zinc-900 border-zinc-800">
            <CardHeader>
              <CardTitle className="text-white">Monthly Revenue</CardTitle>
              <p className="text-sm text-zinc-400">Revenue and ticket sales over the last 6 months</p>
            </CardHeader>
            <CardContent>
              <SimpleChart data={mockData.monthlyRevenue} />
            </CardContent>
          </Card>

          {/* Recent Events */}
          <Card className="bg-zinc-900 border-zinc-800">
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle className="text-white">Recent Events</CardTitle>
                <p className="text-sm text-zinc-400">Your latest event performance</p>
              </div>
              <Button
                size="sm"
                className="bg-red-600 hover:bg-red-700"
                onClick={() => router.push("/dashboard/events/create")}
              >
                <Plus className="h-4 w-4 mr-2" />
                Create Event
              </Button>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mockData.recentEvents.map((event) => (
                  <div key={event.id} className="flex items-center justify-between p-3 bg-zinc-800 rounded-lg">
                    <div>
                      <h4 className="font-medium text-white">{event.name}</h4>
                      <p className="text-sm text-zinc-400">{event.tickets} tickets sold</p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium text-white">${event.revenue.toLocaleString()}</p>
                      <span
                        className={`text-xs px-2 py-1 rounded-full ${
                          event.status === "active" ? "bg-green-900 text-green-300" : "bg-zinc-700 text-zinc-300"
                        }`}
                      >
                        {event.status}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <Card className="bg-zinc-900 border-zinc-800">
          <CardHeader>
            <CardTitle className="text-white">Quick Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Button
                variant="outline"
                className="h-20 flex flex-col gap-2 border-zinc-700 hover:bg-zinc-800"
                onClick={() => router.push("/dashboard/events/create")}
              >
                <Plus className="h-6 w-6" />
                Create New Event
              </Button>
              <Button
                variant="outline"
                className="h-20 flex flex-col gap-2 border-zinc-700 hover:bg-zinc-800"
                onClick={() => router.push("/dashboard/attendees")}
              >
                <Users className="h-6 w-6" />
                View Attendees
              </Button>
              <Button
                variant="outline"
                className="h-20 flex flex-col gap-2 border-zinc-700 hover:bg-zinc-800"
                onClick={() => router.push("/dashboard/scanner")}
              >
                <QrCode className="h-6 w-6" />
                Scan Tickets
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
