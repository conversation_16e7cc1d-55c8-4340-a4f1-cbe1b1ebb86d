const AuthService = require("../services/authService");
const EmailService = require("../services/emailService");
const { createClient } = require("@supabase/supabase-js");
const { PrismaClient } = require("@prisma/client");
const jwt = require("jsonwebtoken");
const { generateToken } = require("../middleware/jwtCookieMiddleware");

const prisma = new PrismaClient();

// JWT Configuration
const JWT_SECRET = process.env.JWT_SECRET || "fitfaat-secret-key";

// JWT Utility Functions
const verifyToken = (token) => {
  try {
    const verified = jwt.verify(token, JWT_SECRET);

    // Ensure both id and user_id are available for compatibility
    if (verified.id && !verified.user_id) {
      verified.user_id = verified.id;
    } else if (verified.user_id && !verified.id) {
      verified.id = verified.user_id;
    }

    // Log the token data for debugging

    return verified;
  } catch (error) {
    console.error("Token verification error:", error);
    throw error;
  }
};

// generateToken function is now imported from jwtCookieMiddleware

// Initialize services
const authService = AuthService.getInstance();
const emailService = EmailService.getInstance();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const supabase = createClient(supabaseUrl, supabaseServiceKey);

class AuthController {
  // Register with email/password
  static async register(req, res) {
    try {
      const { email, password, firstName, lastName, phoneNumber } = req.body;

      // Register user
      const result = await authService.registerWithEmail(
        email,
        password,
        firstName,
        lastName,
        phoneNumber || ""
      );

      // Send verification email
      try {
        await emailService.sendVerificationEmail(
          email,
          result.verificationToken,
          `${firstName} ${lastName}`
        );
      } catch (emailError) {
        console.error("Failed to send verification email:", emailError);
        // Don't fail registration if email fails
      }

      res.status(201).json({
        success: true,
        message:
          "Registration successful. Please check your email to verify your account.",
        data: {
          accountId: result.account.account_id,
          email: result.account.email,
          emailVerified: result.account.email_verified,
        },
      });
    } catch (error) {
      console.error("Registration error:", error);

      if (error.message === "User already exists with this email") {
        return res.status(409).json({
          success: false,
          message: "An account with this email already exists",
        });
      }

      res.status(500).json({
        success: false,
        message: "Registration failed. Please try again.",
      });
    }
  }

  // Login with email/password
  static async login(req, res) {
    try {
      const { email, password } = req.body;

      const result = await authService.loginWithEmail(email, password);

      // Prepare user data for token generation
      const user = {
        user_id: result.account.account_id,
        id: result.account.account_id,
        email: result.account.email,
        role_type: result.account.role_type,
        first_name: result.userDetails?.first_name,
        last_name: result.userDetails?.last_name,
      };

      // Generate JWT token
      const token = generateToken(user);

      // Set token in HTTP-only cookie
      res.cookie("token", token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
        sameSite: "strict",
      });

      res.json({
        success: true,
        message: "Login successful",
        data: {
          user: {
            accountId: result.account.account_id,
            email: result.account.email,
            roleType: result.account.role_type,
            emailVerified: result.account.email_verified,
            profile: result.userDetails,
          },
        },
      });
    } catch (error) {
      console.error("Login error:", error);

      if (error.message === "Invalid email or password") {
        return res.status(401).json({
          success: false,
          message: "Invalid email or password",
        });
      }

      if (error.message === "Please verify your email before logging in") {
        return res.status(403).json({
          success: false,
          message: "Please verify your email before logging in",
        });
      }

      res.status(500).json({
        success: false,
        message: "Login failed. Please try again.",
      });
    }
  }

  // Initiate OAuth
  static async initiateOAuth(req, res) {
    try {
      const { provider } = req.params;

      if (provider !== "google") {
        return res.status(400).json({
          success: false,
          message: "Unsupported OAuth provider",
        });
      }

      // Generate OAuth URL with Supabase
      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: "google",
        options: {
          redirectTo: `${process.env.SERVER_URL}/api/auth/oauth/google/callback`,
        },
      });

      if (error) {
        throw error;
      }

      res.json({
        success: true,
        data: {
          url: data.url,
        },
      });
    } catch (error) {
      console.error("OAuth initiation error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to initiate OAuth",
      });
    }
  }

  // OAuth login/callback
  static async oauthCallback(req, res) {
    try {
      const { provider } = req.params;
      const { code, state } = req.query;
      console.log(req.query);
      if (!code) {
        return res.status(400).json({
          success: false,
          message: "Authorization code required",
        });
      }

      // Exchange code for tokens with Supabase
      const { data, error } = await supabase.auth.exchangeCodeForSession(code);

      if (error) {
        throw error;
      }

      const { user: supabaseUser } = data;

      // Handle OAuth callback
      const result = await authService.handleOAuthCallback(provider, {
        email: supabaseUser.email,
        name: supabaseUser.user_metadata.full_name || supabaseUser.email,
        sub: supabaseUser.id,
      });

      // Prepare user data for token generation using jwtCookieMiddleware format
      const user = {
        user_id: result.account.account_id,
        id: result.account.account_id,
        email: result.account.email,
        role_type: result.account.role_type,
        first_name: result.userDetails?.first_name,
        last_name: result.userDetails?.last_name,
      };

      // Generate JWT token using jwtCookieMiddleware
      const token = generateToken(user);

      // Set token in HTTP-only cookie (same as email login)
      res.cookie("token", token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
        sameSite: "strict",
      });

      // Redirect to frontend with success (no token in URL)
      const redirectUrl = `${process.env.CLIENT_URL}/auth/callback?success=true`;
      res.redirect(redirectUrl);
    } catch (error) {
      console.error("OAuth callback error:", error);

      // Redirect to frontend with error
      const redirectUrl = `${process.env.CLIENT_URL}/auth/callback?error=oauth_failed`;
      res.redirect(redirectUrl);
    }
  }

  // Get OAuth URL
  static async getOAuthUrl(req, res) {
    try {
      const { provider } = req.params;

      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: provider,
        options: {
          redirectTo: `${process.env.SERVER_URL}/api/auth/callback/${provider}`,
        },
      });

      if (error) {
        throw error;
      }

      res.json({
        success: true,
        data: {
          url: data.url,
        },
      });
    } catch (error) {
      console.error("OAuth URL error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to generate OAuth URL",
      });
    }
  }

  // Sync OAuth user data from Supabase session
  static async syncOAuthUser(req, res) {
    try {
      const { supabaseUserData } = req.body;

      if (!supabaseUserData || !supabaseUserData.email) {
        return res.status(400).json({
          success: false,
          message: "Supabase user data is required",
        });
      }

      // Sync user data with database
      const result = await authService.syncOAuthUser(supabaseUserData);

      // Prepare user data for token generation using jwtCookieMiddleware format
      const user = {
        user_id: result.account.account_id,
        id: result.account.account_id,
        email: result.account.email,
        role_type: result.account.role_type,
        first_name: result.userDetails?.first_name,
        last_name: result.userDetails?.last_name,
      };

      // Generate JWT token using jwtCookieMiddleware
      const token = generateToken(user);

      // Set token in HTTP-only cookie (same as email login)
      res.cookie("token", token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
        sameSite: "strict",
      });

      res.json({
        success: true,
        message: "User data synced successfully",
        data: {
          user: {
            accountId: result.account.account_id,
            email: result.account.email,
            roleType: result.account.role_type,
            emailVerified: result.account.email_verified,
            profile: result.userDetails,
          },
          // No accessToken returned - using HTTP-only cookies instead
        },
      });
    } catch (error) {
      console.error("OAuth sync error:", error);
      res.status(500).json({
        success: false,
        message: error.message || "Failed to sync OAuth user data",
      });
    }
  }

  // Verify email
  static async verifyEmail(req, res) {
    try {
      const { token } = req.query;

      const account = await authService.verifyEmail(token);

      // Send welcome email
      try {
        // Get user details for welcome email
        const userDetails = await prisma.users.findUnique({
          where: { user_id: account.role_id },
        });

        await emailService.sendWelcomeEmail(
          account.email,
          `${userDetails.first_name} ${userDetails.last_name}`
        );
      } catch (emailError) {
        console.error("Failed to send welcome email:", emailError);
      }

      res.json({
        success: true,
        message: "Email verified successfully",
      });
    } catch (error) {
      console.error("Email verification error:", error);

      if (
        error.message.includes("Invalid") ||
        error.message.includes("expired") ||
        error.message.includes("used")
      ) {
        return res.status(400).json({
          success: false,
          message: error.message,
        });
      }

      res.status(500).json({
        success: false,
        message: "Email verification failed",
      });
    }
  }

  // Validate session token
  static async validateSession(req, res) {
    try {
      // If we reach here, the JWT cookie middleware has already validated the token
      // and populated req.user
      res.json({
        success: true,
        message: "Session is valid",
        data: {
          user: {
            accountId: req.user.accountId,
            email: req.user.email,
            roleType: req.user.roleType,
            name: req.user.name,
          },
        },
      });
    } catch (error) {
      console.error("Session validation error:", error);
      res.status(401).json({
        success: false,
        message: "Invalid session",
      });
    }
  }

  // Logout
  static async logout(req, res) {
    try {
      const { accountId } = req.user;

      await authService.logout(accountId);

      // Clear JWT token cookie
      res.clearCookie("token");

      res.json({
        success: true,
        message: "Logged out successfully",
      });
    } catch (error) {
      console.error("Logout error:", error);

      // Clear cookie even if logout fails
      res.clearCookie("token");

      res.status(500).json({
        success: false,
        message: "Logout failed",
      });
    }
  }

  // Get current user
  static async getCurrentUser(req, res) {
    try {
      // The middleware already fetches and structures the user data with profile
      res.json({
        success: true,
        data: {
          user: req.user,
        },
      });
    } catch (error) {
      console.error("Get current user error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to get user information",
      });
    }
  }

  // Resend verification email
  static async resendVerificationEmail(req, res) {
    try {
      const { email } = req.body;

      // Check if account exists and is not verified
      const account = await prisma.masteraccounts.findUnique({
        where: { email },
      });

      if (!account) {
        return res.status(404).json({
          success: false,
          message: "Account not found",
        });
      }

      if (account.email_verified) {
        return res.status(400).json({
          success: false,
          message: "Email is already verified",
        });
      }

      // Generate new verification token
      const verificationToken = authService.generateEmailVerificationToken();
      const expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + 24);

      await prisma.emailverificationtokens.create({
        data: {
          account_id: account.account_id,
          token: verificationToken,
          expires_at: expiresAt,
          used: false,
          created_at: new Date(),
        },
      });

      // Get user details
      const userDetails = await prisma.users.findUnique({
        where: { user_id: account.role_id },
      });

      // Send verification email
      await emailService.sendVerificationEmail(
        email,
        verificationToken,
        `${userDetails.first_name} ${userDetails.last_name}`
      );

      res.json({
        success: true,
        message: "Verification email sent successfully",
      });
    } catch (error) {
      console.error("Resend verification email error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to send verification email",
      });
    }
  }

  // Forgot password
  static async forgotPassword(req, res) {
    try {
      const { email } = req.body;

      if (!email) {
        return res.status(400).json({
          success: false,
          message: "Email is required",
        });
      }

      // Check if account exists
      const account = await prisma.masteraccounts.findUnique({
        where: { email },
      });

      if (!account) {
        // Don't reveal if email exists or not for security
        return res.json({
          success: true,
          message:
            "If an account with this email exists, password reset instructions have been sent",
        });
      }

      // Generate reset token
      const resetToken = authService.generateResetToken();
      const expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + 1); // 1 hour expiry

      // Update account with reset token
      await prisma.masteraccounts.update({
        where: { account_id: account.account_id },
        data: {
          reset_token: resetToken,
          reset_token_expires: expiresAt,
          updated_at: new Date(),
        },
      });

      // Get user details
      const userDetails = await prisma.users.findUnique({
        where: { account_id: account.account_id },
      });

      // Send password reset email
      await emailService.sendPasswordResetEmail(
        email,
        resetToken,
        userDetails
          ? `${userDetails.first_name} ${userDetails.last_name}`
          : "User"
      );

      res.json({
        success: true,
        message:
          "If an account with this email exists, password reset instructions have been sent",
      });
    } catch (error) {
      console.error("Forgot password error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to process password reset request",
      });
    }
  }

  // Reset password
  static async resetPassword(req, res) {
    try {
      const { token, newPassword } = req.body;

      if (!token || !newPassword) {
        return res.status(400).json({
          success: false,
          message: "Token and new password are required",
        });
      }

      if (newPassword.length < 8) {
        return res.status(400).json({
          success: false,
          message: "Password must be at least 8 characters long",
        });
      }

      // Find account with valid reset token
      const account = await prisma.masteraccounts.findFirst({
        where: {
          reset_token: token,
          reset_token_expires: {
            gt: new Date(),
          },
        },
      });

      if (!account) {
        return res.status(400).json({
          success: false,
          message: "Invalid or expired reset token",
        });
      }

      // Hash new password
      const hashedPassword = await authService.hashPassword(newPassword);

      // Update password and clear reset token
      await prisma.masteraccounts.update({
        where: { account_id: account.account_id },
        data: {
          password_hash: hashedPassword,
          reset_token: null,
          reset_token_expires: null,
          updated_at: new Date(),
        },
      });

      res.json({
        success: true,
        message: "Password reset successfully",
      });
    } catch (error) {
      console.error("Reset password error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to reset password",
      });
    }
  }

  // Update profile
  static async updateProfile(req, res) {
    try {
      const { firstName, lastName, phoneNumber, gender, dob } = req.body;
      const { accountId } = req.user;

      // Get user details
      const userDetails = await prisma.users.findUnique({
        where: { account_id: accountId },
      });

      if (!userDetails) {
        return res.status(404).json({
          success: false,
          message: "User profile not found",
        });
      }

      // Update user profile
      const updatedUser = await prisma.users.update({
        where: { account_id: accountId },
        data: {
          first_name: firstName || userDetails.first_name,
          last_name: lastName || userDetails.last_name,
          phone_number: phoneNumber || userDetails.phone_number,
          gender: gender || userDetails.gender,
          dob: dob ? new Date(dob) : userDetails.dob,
          updated_at: new Date(),
        },
      });

      res.json({
        success: true,
        message: "Profile updated successfully",
        data: {
          profile: updatedUser,
        },
      });
    } catch (error) {
      console.error("Update profile error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to update profile",
      });
    }
  }

  // Change password
  static async changePassword(req, res) {
    try {
      const { currentPassword, newPassword } = req.body;
      const { accountId } = req.user;

      if (!currentPassword || !newPassword) {
        return res.status(400).json({
          success: false,
          message: "Current password and new password are required",
        });
      }

      if (newPassword.length < 8) {
        return res.status(400).json({
          success: false,
          message: "New password must be at least 8 characters long",
        });
      }

      // Get account
      const account = await prisma.masteraccounts.findUnique({
        where: { account_id: accountId },
      });

      if (!account || !account.password_hash) {
        return res.status(400).json({
          success: false,
          message: "Cannot change password for OAuth accounts",
        });
      }

      // Verify current password
      const isValidPassword = await authService.comparePassword(
        currentPassword,
        account.password_hash
      );
      if (!isValidPassword) {
        return res.status(400).json({
          success: false,
          message: "Current password is incorrect",
        });
      }

      // Hash new password
      const hashedPassword = await authService.hashPassword(newPassword);

      // Update password
      await prisma.masteraccounts.update({
        where: { account_id: accountId },
        data: {
          password_hash: hashedPassword,
          updated_at: new Date(),
        },
      });

      res.json({
        success: true,
        message: "Password changed successfully",
      });
    } catch (error) {
      console.error("Change password error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to change password",
      });
    }
  }
}

module.exports = AuthController;
