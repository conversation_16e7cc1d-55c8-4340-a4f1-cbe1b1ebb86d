"use client";

import { useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { motion } from "framer-motion";
import { XCircle, ArrowLeft, RefreshCw } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/context/auth-context";
import { toast } from "sonner";
import Navbar from "@/components/navbar";
import Footer from "@/components/footer";

export default function PaymentFailPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user } = useAuth();

  const error = searchParams.get("error");

  useEffect(() => {
    if (!user) {
      router.push("/");
      return;
    }

    if (error) {
      toast.error(decodeURIComponent(error));
    }
  }, [user, error]);

  const handleRetryPayment = () => {
    router.push("/checkout");
  };

  const handleGoToDashboard = () => {
    router.push("/user-dashboard");
  };

  const handleBrowseEvents = () => {
    router.push("/events");
  };

  return (
    <div className="min-h-screen bg-background">
      <Navbar />
      
      <main className="pt-20 pb-12">
        <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center"
          >
            {/* Error Icon */}
            <div className="w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <XCircle className="w-10 h-10 text-red-600" />
            </div>

            {/* Error Message */}
            <h1 className="text-3xl font-bold text-text-primary mb-4">
              Payment Failed
            </h1>
            
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-8">
              <p className="text-red-800 text-lg">
                {error ? decodeURIComponent(error) : "Your payment could not be processed. Please try again."}
              </p>
            </div>

            {/* Common Reasons */}
            <div className="bg-background-50 rounded-lg p-6 mb-8 text-left">
              <h2 className="text-lg font-semibold mb-4 text-center">Common Reasons for Payment Failure</h2>
              <ul className="space-y-2 text-text-secondary">
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-primary-600 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  Insufficient funds in your account
                </li>
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-primary-600 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  Incorrect card details or expired card
                </li>
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-primary-600 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  Network connectivity issues
                </li>
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-primary-600 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  Bank security restrictions
                </li>
                <li className="flex items-start">
                  <span className="w-2 h-2 bg-primary-600 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                  Payment gateway timeout
                </li>
              </ul>
            </div>

            {/* Action Buttons */}
            <div className="space-y-4">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
              >
                <Button
                  onClick={handleRetryPayment}
                  className="w-full sm:w-auto px-8 py-3 text-lg"
                >
                  <RefreshCw className="w-5 h-5 mr-2" />
                  Try Payment Again
                </Button>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="flex flex-col sm:flex-row gap-4 justify-center"
              >
                <Button
                  variant="outline"
                  onClick={handleGoToDashboard}
                  className="flex items-center"
                >
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Go to Dashboard
                </Button>
                
                <Button
                  variant="outline"
                  onClick={handleBrowseEvents}
                  className="flex items-center"
                >
                  Browse Events
                </Button>
              </motion.div>
            </div>

            {/* Help Section */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="mt-12 p-6 bg-background-50 rounded-lg"
            >
              <h3 className="text-lg font-semibold mb-2">Need Help?</h3>
              <p className="text-text-secondary mb-4">
                If you continue to experience payment issues, please contact our support team.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <a
                  href="mailto:<EMAIL>"
                  className="text-primary-600 hover:text-primary-700 font-medium"
                >
                  <EMAIL>
                </a>
                <span className="hidden sm:inline text-text-secondary">|</span>
                <a
                  href="tel:+8801234567890"
                  className="text-primary-600 hover:text-primary-700 font-medium"
                >
                  +880 ************
                </a>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
