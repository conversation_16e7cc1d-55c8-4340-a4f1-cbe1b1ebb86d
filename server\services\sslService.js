const { PrismaClient } = require("@prisma/client");
const SSLCommerzPayment = require("sslcommerz-lts");

const prisma = new PrismaClient();

const store_id = process.env.SSL_STORE_ID;
const store_passwd = process.env.SSL_STORE_PASS;
const is_live = true; //true for live, false for sandbox

class SSLService {
  static instance = null;

  static getInstance() {
    if (!SSLService.instance) {
      SSLService.instance = new SSLService();
    }
    return SSLService.instance;
  }

  async initiatePayment(orderData) {
    try {
      if (!orderData) {
        throw new Error("Order data is required");
      }

      // Calculate total amount from order items
      const totalAmount = orderData.orderitems.reduce((sum, item) => {
        return sum + parseFloat(item.unit_price) * item.quantity;
      }, 0);

      // Get event and user information
      const firstOrderItem = orderData.orderitems[0];
      const event = firstOrderItem.tickettypes.events;

      // Get user information from order
      const user = await prisma.users.findUnique({
        where: { user_id: orderData.user_id },
        include: {
          masteraccounts: true,
        },
      });

      if (!user) {
        throw new Error("User not found");
      }

      // Generate unique transaction ID
      const transactionId = `TXN_${orderData.order_id}_${Date.now()}`;

      // Prepare SSL Commerce data
      const data = {
        total_amount: totalAmount.toFixed(2),
        currency: "BDT",
        tran_id: transactionId,
        success_url: `${process.env.SERVER_URL}/api/ssl/success`,
        fail_url: `${process.env.SERVER_URL}/api/ssl/fail`,
        cancel_url: `${process.env.SERVER_URL}/api/ssl/cancel`,
        ipn_url: `${process.env.SERVER_URL}/api/ssl/ipn`,

        payment_method: "card",

        // Event information
        event_name: event.title,
        event_start_date: event.start_date,

        // Product information
        product_name: `Event Tickets - ${event.title}`,
        product_category: "Event Tickets",
        product_profile: "general",

        // Customer information
        cus_name: `${user.first_name} ${user.last_name}`,
        cus_email: user.masteraccounts.email,
        cus_add1: "Customer Address",
        cus_city: "Dhaka",
        cus_state: "Dhaka",
        cus_postcode: "1000",
        cus_country: "Bangladesh",
        cus_phone: user.phone_number || "***********",

        // Shipping information (same as customer)
        shipping_method: "mail",
        ship_name: `${user.first_name} ${user.last_name}`,
        ship_add1: "Customer Address",
        ship_city: "Dhaka",
        ship_state: "Dhaka",
        ship_postcode: "1000",
        ship_country: "Bangladesh",

        // Additional fields
        value_a: orderData.order_id.toString(),
        value_b: orderData.user_id.toString(),
        value_c: event.event_id.toString(),
      };

      const sslcz = new SSLCommerzPayment(store_id, store_passwd, is_live);
      const apiResponse = await sslcz.init(data);

      if (apiResponse.status === "SUCCESS") {
        // Store transaction ID in order for tracking
        await prisma.orders.update({
          where: { order_id: orderData.order_id },
          data: { transaction_id: transactionId },
        });

        return {
          success: true,
          data: {
            url: apiResponse.GatewayPageURL,
            sessionkey: apiResponse.sessionkey,
            transactionId: transactionId,
          },
          message: "Payment gateway URL generated successfully",
        };
      } else {
        throw new Error(
          apiResponse.failedreason || "Failed to initialize payment"
        );
      }
    } catch (error) {
      console.error("Error initiating payment:", error);
      return {
        success: false,
        message: error.message || "Failed to initiate payment",
        data: null,
      };
    }
  }

  async validatePayment(validationData) {
    try {
      if (!validationData || !validationData.tran_id) {
        throw new Error("Transaction ID is required for validation");
      }

      const sslcz = new SSLCommerzPayment(store_id, store_passwd, is_live);
      const response = await sslcz.validate(validationData);

      if (response.status === "VALID") {
        // Find the order by transaction ID
        const order = await prisma.orders.findFirst({
          where: { transaction_id: validationData.tran_id },
        });

        if (!order) {
          throw new Error("Order not found for this transaction");
        }

        // Update order payment status to completed
        await prisma.orders.update({
          where: { order_id: order.order_id },
          data: {
            payment_status: "completed",
            payment_method: "sslcommerz",
          },
        });

        return {
          success: true,
          data: {
            orderId: order.order_id,
            transactionId: validationData.tran_id,
            amount: response.amount,
            status: response.status,
          },
          message: "Payment validated successfully",
        };
      } else {
        throw new Error("Payment validation failed");
      }
    } catch (error) {
      console.error("Error validating payment:", error);
      return {
        success: false,
        message: error.message || "Payment validation failed",
        data: null,
      };
    }
  }

  async handlePaymentSuccess(paymentData) {
    try {
      if (!paymentData || !paymentData.tran_id) {
        throw new Error("Transaction data is required");
      }

      // Validate the payment first
      const validationResult = await this.validatePayment(paymentData);

      if (!validationResult.success) {
        throw new Error(validationResult.message);
      }

      // Get the order with all related data
      const order = await prisma.orders.findUnique({
        where: { order_id: validationResult.data.orderId },
        include: {
          orderitems: {
            include: {
              tickettypes: {
                include: {
                  events: true,
                },
              },
            },
          },
          users: {
            include: {
              masteraccounts: true,
            },
          },
        },
      });

      if (!order) {
        throw new Error("Order not found");
      }

      return {
        success: true,
        data: {
          order,
          transactionId: paymentData.tran_id,
          amount: paymentData.amount,
        },
        message: "Payment processed successfully",
      };
    } catch (error) {
      console.error("Error processing payment success:", error);
      return {
        success: false,
        message: error.message || "Failed to process payment success",
        data: null,
      };
    }
  }

  async handlePaymentFailure(paymentData) {
    try {
      if (paymentData && paymentData.tran_id) {
        // Find and update order status to failed with retry logic
        const maxRetries = 3;
        let retryCount = 0;

        while (retryCount < maxRetries) {
          try {
            const order = await prisma.orders.findFirst({
              where: {
                transaction_id: paymentData.tran_id,
                payment_status: "pending",
              },
            });

            if (order) {
              await prisma.orders.update({
                where: { order_id: order.order_id },
                data: {
                  payment_status: "failed",
                  updated_at: new Date(),
                },
              });
              break; // Success, exit retry loop
            }
            break; // Order not found or already processed
          } catch (dbError) {
            retryCount++;
            if (retryCount >= maxRetries) {
              console.error(
                "Failed to update order status after retries:",
                dbError
              );
            } else {
              // Wait before retry
              await new Promise((resolve) =>
                setTimeout(resolve, 1000 * retryCount)
              );
            }
          }
        }
      }

      return {
        success: false,
        message: paymentData?.fail_reason || "Payment failed",
        data: paymentData,
      };
    } catch (error) {
      console.error("Error handling payment failure:", error);
      return {
        success: false,
        message: "Failed to process payment failure",
        data: null,
      };
    }
  }

  /**
   * Enhanced security validation for payment data
   */
  async validatePaymentSecurity(paymentData, orderData) {
    try {
      // Validate required fields
      const requiredFields = ["tran_id", "val_id", "amount", "card_type"];
      for (const field of requiredFields) {
        if (!paymentData[field]) {
          throw new Error(`Missing required field: ${field}`);
        }
      }

      // Validate amount matches order
      const expectedAmount = parseFloat(orderData.total_amount);
      const paidAmount = parseFloat(paymentData.amount);

      if (Math.abs(expectedAmount - paidAmount) > 0.01) {
        throw new Error("Payment amount mismatch");
      }

      // Validate transaction timestamp (prevent replay attacks)
      if (paymentData.tran_date) {
        const transactionDate = new Date(paymentData.tran_date);
        const now = new Date();
        const timeDiff = now - transactionDate;
        const maxAge = 30 * 60 * 1000; // 30 minutes

        if (timeDiff > maxAge) {
          throw new Error("Transaction too old");
        }
      }

      // Validate order belongs to correct user
      if (orderData.user_id !== paymentData.value_b) {
        throw new Error("User ID mismatch");
      }

      return { valid: true };
    } catch (error) {
      console.error("Payment security validation failed:", error);
      return { valid: false, error: error.message };
    }
  }

  /**
   * Handle network failures and retries
   */
  async withRetry(operation, maxRetries = 3, delay = 1000) {
    let lastError;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;

        // Don't retry on validation errors
        if (
          error.message.includes("validation") ||
          error.message.includes("mismatch") ||
          error.message.includes("not found")
        ) {
          throw error;
        }

        if (attempt < maxRetries) {
          console.warn(
            `Operation failed, retrying (${attempt}/${maxRetries}):`,
            error.message
          );
          await new Promise((resolve) => setTimeout(resolve, delay * attempt));
        }
      }
    }

    throw lastError;
  }
}

module.exports = SSLService;
