"use client"

import { useEffect, useState } from "react"
import { useSearchPara<PERSON>, useRouter } from "next/navigation"
import { useAuth } from "@/context/auth-context"
import { supabase } from "@/lib/supabaseClient"
import { Button } from "@/components/ui/button"
import { CheckCircle, XCircle, Loader2 } from "lucide-react"

export default function AuthCallbackPage() {
  const [status, setStatus] = useState('processing') // processing, success, error
  const [message, setMessage] = useState('')
  const searchParams = useSearchParams()
  const router = useRouter()
  const { session } = useAuth()

  useEffect(() => {
    handleCallback()
  }, [searchParams])

  const handleCallback = async () => {
    try {
      // Handle Supabase OAuth callback
      const { data, error } = await supabase.auth.getSession()

      if (error) {
        console.error('Supabase session error:', error)
        setStatus('error')
        setMessage('Authentication failed. Please try again.')
        return
      }

      if (data.session) {
        setStatus('success')
        setMessage('Successfully signed in with Google!')

        // Redirect to home page after 2 seconds
        setTimeout(() => {
          router.push('/')
        }, 2000)
      } else {
        // Check for URL parameters (new cookie-based flow)
        const success = searchParams.get('success')
        const errorParam = searchParams.get('error')

        if (errorParam) {
          setStatus('error')
          setMessage(getErrorMessage(errorParam))
          return
        }

        if (success === 'true') {
          setStatus('success')
          setMessage('Successfully signed in!')

          setTimeout(() => {
            router.push('/')
          }, 2000)
        } else {
          setStatus('error')
          setMessage('Authentication failed. Please try again.')
        }
      }
    } catch (error) {
      console.error('Callback error:', error)
      setStatus('error')
      setMessage('Authentication failed. Please try again.')
    }
  }

  const getErrorMessage = (error) => {
    switch (error) {
      case 'access_denied':
        return 'You cancelled the authentication process.'
      case 'oauth_failed':
        return 'OAuth authentication failed. Please try again.'
      case 'server_error':
        return 'Server error occurred. Please try again later.'
      default:
        return 'Authentication failed. Please try again.'
    }
  }

  return (
    <div className="min-h-screen bg-zinc-950 flex items-center justify-center p-4">
      <div className="max-w-md w-full bg-zinc-900 rounded-lg p-8 text-center">
        {status === 'processing' && (
          <>
            <Loader2 className="h-16 w-16 text-red-500 mx-auto mb-4 animate-spin" />
            <h1 className="text-2xl font-bold text-white mb-2">Processing Authentication</h1>
            <p className="text-zinc-400">Please wait while we complete your sign-in...</p>
          </>
        )}

        {status === 'success' && (
          <>
            <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
            <h1 className="text-2xl font-bold text-white mb-2">Welcome!</h1>
            <p className="text-zinc-400 mb-6">{message}</p>
            <p className="text-sm text-zinc-500">Redirecting you to your dashboard...</p>
          </>
        )}

        {status === 'error' && (
          <>
            <XCircle className="h-16 w-16 text-red-500 mx-auto mb-4" />
            <h1 className="text-2xl font-bold text-white mb-2">Authentication Failed</h1>
            <p className="text-zinc-400 mb-6">{message}</p>

            <div className="space-y-3">
              <Button
                onClick={() => router.push('/')}
                className="w-full bg-red-600 hover:bg-red-700"
              >
                Try Again
              </Button>

              <Button
                variant="outline"
                onClick={() => router.push('/')}
                className="w-full"
              >
                Back to Homepage
              </Button>
            </div>
          </>
        )}
      </div>
    </div>
  )
}
