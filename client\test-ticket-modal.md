# Ticket Info Modal Test Plan

## Test Scenarios

### 1. Basic Modal Functionality
- [ ] Mo<PERSON> opens when "Buy Now" is clicked with selected tickets
- [ ] Modal displays correct event information
- [ ] <PERSON><PERSON> shows progress stepper with correct step count
- [ ] Modal can be closed with X button
- [ ] Mo<PERSON> prevents background interaction

### 2. Single Ticket Test
- [ ] Select 1 ticket of any type
- [ ] Click "Buy Now"
- [ ] Verify modal shows "Step 1 of 1"
- [ ] Fill in attendee information (name, email, phone)
- [ ] Verify form validation (all fields required)
- [ ] Click "Complete Purchase"
- [ ] Verify redirect to checkout

### 3. Multiple Tickets Same Type Test
- [ ] Select 1 ticket type with quantity 3
- [ ] Click "Buy Now"
- [ ] Verify modal shows "Step 1 of 3"
- [ ] Fill information for first attendee
- [ ] Click "Next" and verify step 2
- [ ] Fill information for second attendee
- [ ] Click "Next" and verify step 3
- [ ] Fill information for third attendee
- [ ] Click "Complete Purchase"
- [ ] Verify all attendee info is collected

### 4. Multiple Ticket Types Test
- [ ] Select 2 different ticket types (1 each)
- [ ] Click "Buy Now"
- [ ] Verify modal shows "Step 1 of 2"
- [ ] Verify correct ticket type info displayed
- [ ] Fill information for first ticket
- [ ] Click "Next"
- [ ] Verify second ticket type info displayed
- [ ] Fill information for second ticket
- [ ] Click "Complete Purchase"

### 5. Form Validation Test
- [ ] Try to proceed with empty name field
- [ ] Try to proceed with empty email field
- [ ] Try to proceed with empty phone field
- [ ] Try to proceed with invalid email format
- [ ] Verify "Next" button is disabled when form invalid
- [ ] Verify "Complete Purchase" button is disabled when form invalid

### 6. Navigation Test
- [ ] Test "Previous" button functionality
- [ ] Verify "Previous" is disabled on first step
- [ ] Test form data persistence when navigating back/forward
- [ ] Test closing modal mid-process and reopening

### 7. Category Display Test
- [ ] Select tickets from different categories
- [ ] Verify category name is displayed correctly in modal
- [ ] Verify ticket type name is displayed correctly

### 8. Integration Test
- [ ] Complete full flow with attendee info
- [ ] Verify tickets are added to cart
- [ ] Verify attendee info is stored in sessionStorage
- [ ] Verify redirect to checkout page
- [ ] Check console for any errors

## Manual Testing Steps

1. **Setup**: Ensure both frontend (port 3000) and backend (port 5000) servers are running
2. **Navigate**: Go to http://localhost:3000
3. **Login**: Create account or login with existing credentials
4. **Browse Events**: Navigate to an event detail page
5. **Select Tickets**: Choose one or more tickets with different quantities
6. **Test Modal**: Click "Buy Now" and follow test scenarios above

## Expected Behavior

- Modal should match the design patterns from AuthModal
- Form styling should be consistent with existing forms
- Progress indicator should work smoothly
- All form data should be validated
- Navigation between steps should be intuitive
- Final submission should integrate with existing cart system

## Success Criteria

- ✅ Modal opens and displays correctly
- ✅ Stepper shows accurate progress
- ✅ Form validation works properly
- ✅ Navigation between steps works
- ✅ Attendee information is collected for each ticket
- ✅ Integration with cart system works
- ✅ Checkout redirect works with attendee data
- ✅ No console errors or warnings
- ✅ Responsive design works on mobile
