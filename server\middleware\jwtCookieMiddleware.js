const jwt = require("jsonwebtoken");
const { PrismaClient } = require("@prisma/client");
const prisma = new PrismaClient();

// Secret key for JWT - in production, this should be in an environment variable
const JWT_SECRET = process.env.JWT_SECRET || "fitfaat-secret-key";

// Middleware to verify JWT token from cookies
const verifyTokenFromCookie = async (req, res, next) => {
  const token = req.cookies.token;

  if (!token) {
    return res.status(401).json({ error: "Access denied. No token provided." });
  }

  try {
    // Verify the token
    const verified = jwt.verify(token, JWT_SECRET);

    // Ensure both id and user_id are available for compatibility
    if (verified.id && !verified.user_id) {
      verified.user_id = verified.id;
    } else if (verified.user_id && !verified.id) {
      verified.id = verified.user_id;
    }

    // Get account details with email verification status
    const account = await prisma.masteraccounts.findUnique({
      where: { account_id: verified.id },
      select: {
        account_id: true,
        email: true,
        role_type: true,
        email_verified: true,
        role_id: true,
      },
    });

    if (!account) {
      return res
        .status(401)
        .json({ error: "Invalid token - account not found" });
    }

    // Get profile data based on role type
    let profile = null;
    if (account.role_type === "user" && account.role_id) {
      profile = await prisma.users.findUnique({
        where: { user_id: account.role_id },
        select: {
          user_id: true,
          account_id: true,
          first_name: true,
          last_name: true,
          phone_number: true,
          profile_image: true,
          gender: true,
          dob: true,
          created_at: true,
          updated_at: true,
        },
      });
    } else if (account.role_type === "admin" && account.role_id) {
      profile = await prisma.admins.findUnique({
        where: { admin_id: account.role_id },
        select: {
          admin_id: true,
          account_id: true,
          name: true,
          created_at: true,
        },
      });
    } else if (account.role_type === "organizer" && account.role_id) {
      profile = await prisma.organizers.findUnique({
        where: { organizer_id: account.role_id },
        select: {
          organizer_id: true,
          account_id: true,
          organization_name: true,
          phone_number: true,
          created_at: true,
          updated_at: true,
        },
      });
    }

    // Set user data in request object with the new structure
    req.user = {
      accountId: account.account_id,
      email: account.email,
      roleType: account.role_type,
      emailVerified: account.email_verified,
      profile: profile,
      // Keep legacy fields for backward compatibility
      roleId: account.role_id,
      id: account.account_id,
      user_id: account.account_id,
      role: account.role_type,
      name: verified.name,
      iat: verified.iat,
      exp: verified.exp,
    };

    // Proceed to the next middleware or route handler
    next();
  } catch (error) {
    console.error("Token verification error:", error);
    res.status(401).json({ error: "Invalid token" });
  }
};

// Optional JWT cookie middleware - doesn't fail if no token provided
const optionalVerifyTokenFromCookie = async (req, res, next) => {
  const token = req.cookies.token;

  if (!token) {
    req.user = null;
    return next();
  }

  try {
    // Verify the token
    const verified = jwt.verify(token, JWT_SECRET);

    // Ensure both id and user_id are available for compatibility
    if (verified.id && !verified.user_id) {
      verified.user_id = verified.id;
    } else if (verified.user_id && !verified.id) {
      verified.id = verified.user_id;
    }

    // Get account details with email verification status
    const account = await prisma.masteraccounts.findUnique({
      where: { account_id: verified.id },
      select: {
        account_id: true,
        email: true,
        role_type: true,
        email_verified: true,
        role_id: true,
      },
    });

    if (!account) {
      req.user = null;
      return next();
    }

    // Get profile data based on role type
    let profile = null;
    if (account.role_type === "user" && account.role_id) {
      profile = await prisma.users.findUnique({
        where: { user_id: account.role_id },
        select: {
          user_id: true,
          account_id: true,
          first_name: true,
          last_name: true,
          phone_number: true,
          profile_image: true,
          gender: true,
          dob: true,
          created_at: true,
          updated_at: true,
        },
      });
    } else if (account.role_type === "admin" && account.role_id) {
      profile = await prisma.admins.findUnique({
        where: { admin_id: account.role_id },
        select: {
          admin_id: true,
          account_id: true,
          name: true,
          created_at: true,
        },
      });
    } else if (account.role_type === "organizer" && account.role_id) {
      profile = await prisma.organizers.findUnique({
        where: { organizer_id: account.role_id },
        select: {
          organizer_id: true,
          account_id: true,
          organization_name: true,
          phone_number: true,
          created_at: true,
          updated_at: true,
        },
      });
    }

    // Set user data in request object with the new structure
    req.user = {
      accountId: account.account_id,
      email: account.email,
      roleType: account.role_type,
      emailVerified: account.email_verified,
      profile: profile,
      // Keep legacy fields for backward compatibility
      roleId: account.role_id,
      id: account.account_id,
      user_id: account.account_id,
      role: account.role_type,
      name: verified.name,
      iat: verified.iat,
      exp: verified.exp,
    };

    next();
  } catch (error) {
    console.error("Token verification error:", error);
    req.user = null;
    next();
  }
};

// Function to generate JWT token
const generateToken = (user) => {
  // Remove sensitive information and ensure consistent field names
  const userData = {
    id: user.user_id || user.id, // Use user_id from DB or id if already transformed
    email: user.email,
    name: user.first_name
      ? `${user.first_name} ${user.last_name || ""}`.trim()
      : user.name || user.username,
    role: user.role_type || user.role || "user", // Use role_type from DB, fallback to role, default to 'user'
  };

  console.log("Generating token with user data:", userData);

  // Generate token with expiration of 7 days
  return jwt.sign(userData, JWT_SECRET, { expiresIn: "7d" });
};

const JWT_SECRET_KEY = JWT_SECRET;

module.exports = {
  verifyTokenFromCookie,
  optionalVerifyTokenFromCookie,
  generateToken,
  JWT_SECRET_KEY,
};
