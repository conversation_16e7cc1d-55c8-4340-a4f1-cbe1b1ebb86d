"use client";

import { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { motion } from "framer-motion";
import {
  CheckCircle,
  Download,
  Calendar,
  MapPin,
  Clock,
  ArrowRight,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { useAuth } from "@/context/auth-context";
import { ordersAPI, ticketsAPI } from "@/lib/api";
import { toast } from "sonner";
import Navbar from "@/components/navbar";
import Footer from "@/components/footer";

export default function PaymentSuccessPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user } = useAuth();

  const [loading, setLoading] = useState(true);
  const [orderData, setOrderData] = useState(null);
  const [tickets, setTickets] = useState([]);
  const [error, setError] = useState(null);

  const orderId = searchParams.get("orderId");
  const transactionId = searchParams.get("transactionId");

  useEffect(() => {
    if (!user) {
      router.push("/");
      return;
    }

    if (!orderId || !transactionId) {
      setError("Missing payment information");
      setLoading(false);
      return;
    }

    handlePaymentSuccess();
  }, [user, orderId, transactionId]);

  const handlePaymentSuccess = async () => {
    try {
      setLoading(true);

      // Get order details
      const orderResponse = await ordersAPI.getOrderById(orderId);
      if (!orderResponse.success) {
        throw new Error(
          orderResponse.message || "Failed to fetch order details"
        );
      }

      setOrderData(orderResponse.data);

      // Check if payment is completed and get existing tickets
      if (orderResponse.data.payment_status === "completed") {
        // Try to get existing tickets for this order
        try {
          const existingTicketsResponse = await ticketsAPI.getTicketsByOrderId(
            orderId
          );

          if (
            existingTicketsResponse.success &&
            existingTicketsResponse.data.tickets.length > 0
          ) {
            // Tickets already exist (created by server callback)
            setTickets(existingTicketsResponse.data.tickets);
            toast.success("Payment successful! Your tickets are ready.");
          } else {
            // Fallback: Create tickets if they don't exist (for backward compatibility)
            console.log(
              "No existing tickets found, creating tickets as fallback..."
            );
            await createTicketsAsFallback(orderResponse.data);
          }
        } catch (ticketError) {
          console.error("Error fetching existing tickets:", ticketError);
          // Fallback: Create tickets if fetching fails
          await createTicketsAsFallback(orderResponse.data);
        }
      }
    } catch (error) {
      console.error("Error handling payment success:", error);
      setError(error.message);
      toast.error(error.message);
    } finally {
      setLoading(false);
    }
  };

  // Fallback method to create tickets if they weren't created by server callback
  const createTicketsAsFallback = async (orderData) => {
    try {
      // Get attendee info from sessionStorage
      const attendeeInfo = sessionStorage.getItem("ticketAttendeeInfo");
      let ticketsWithAttendeeInfo = [];

      if (attendeeInfo) {
        try {
          const parsedAttendeeInfo = JSON.parse(attendeeInfo);
          ticketsWithAttendeeInfo = parsedAttendeeInfo.attendeeInfo || [];
          // Clear from session storage after use
          sessionStorage.removeItem("ticketAttendeeInfo");
        } catch (error) {
          console.error("Error parsing attendee info:", error);
        }
      }

      // Transform order data to selectedTickets format for createTickets()
      const selectedTickets = [];
      let eventId = null;

      for (const orderItem of orderData.orderitems) {
        selectedTickets.push({
          ticketTypeId: orderItem.ticket_type_id,
          quantity: orderItem.quantity,
          price: parseFloat(orderItem.unit_price),
          name: orderItem.tickettypes.name,
        });

        // Get eventId from the first order item
        if (!eventId) {
          eventId = orderItem.tickettypes.events.event_id;
        }
      }

      console.log("Creating tickets as fallback...");
      console.log("Selected Tickets for createTickets:", selectedTickets);
      console.log("Tickets with Attendee Info:", ticketsWithAttendeeInfo);
      console.log("Event ID:", eventId);

      // Create tickets using createTickets() method with existing order ID
      const ticketResponse = await ticketsAPI.createTickets(
        selectedTickets,
        ticketsWithAttendeeInfo,
        eventId,
        parseInt(orderData.order_id) // Pass the existing order ID
      );

      if (ticketResponse.success) {
        setTickets(ticketResponse.data.tickets || []);
        toast.success("Tickets created successfully!");
      } else {
        console.error("Failed to create tickets:", ticketResponse.message);
        toast.error("Tickets will be created shortly. Check your dashboard.");
      }
    } catch (error) {
      console.error("Error creating tickets as fallback:", error);
      toast.error("Tickets will be created shortly. Check your dashboard.");
    }
  };

  const downloadTicket = async (ticketId) => {
    try {
      const response = await ticketsAPI.downloadTicketPDF(ticketId);

      // Create blob and download
      const blob = new Blob([response.data], { type: "application/pdf" });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = `ticket-${ticketId}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      toast.success("Ticket downloaded successfully!");
    } catch (error) {
      console.error("Error downloading ticket:", error);
      toast.error("Failed to download ticket");
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
          <p className="text-text-secondary">Processing your payment...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-background">
        <Navbar />
        <div className="flex items-center justify-center min-h-[80vh]">
          <div className="text-center max-w-md mx-auto px-4">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg
                className="w-8 h-8 text-red-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </div>
            <h1 className="text-2xl font-bold text-text-primary mb-2">
              Payment Error
            </h1>
            <p className="text-text-secondary mb-6">{error}</p>
            <Button onClick={() => router.push("/user-dashboard")}>
              Go to Dashboard
            </Button>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <Navbar />

      <main className="pt-20 pb-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Success Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center mb-8"
          >
            <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <CheckCircle className="w-10 h-10 text-green-600" />
            </div>
            <h1 className="text-3xl font-bold text-text-primary mb-2">
              Payment Successful!
            </h1>
            <p className="text-text-secondary text-lg">
              Your tickets have been purchased successfully
            </p>
          </motion.div>

          {/* Order Summary */}
          {orderData && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="bg-background-50 rounded-lg p-6 mb-8"
            >
              <h2 className="text-xl font-semibold mb-4">Order Summary</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-text-secondary">Order ID</p>
                  <p className="font-medium">#{orderData.order_id}</p>
                </div>
                <div>
                  <p className="text-sm text-text-secondary">Transaction ID</p>
                  <p className="font-medium">{transactionId}</p>
                </div>
                <div>
                  <p className="text-sm text-text-secondary">Total Amount</p>
                  <p className="font-medium text-lg">
                    ৳{parseFloat(orderData.total_amount).toFixed(2)}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-text-secondary">Payment Status</p>
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    Completed
                  </span>
                </div>
              </div>
            </motion.div>
          )}

          {/* Tickets */}
          {tickets.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="mb-8"
            >
              <h2 className="text-xl font-semibold mb-4">Your Tickets</h2>
              <div className="space-y-4">
                {tickets.map((ticket, index) => (
                  <div
                    key={ticket.ticket_id}
                    className="bg-background-50 rounded-lg p-6"
                  >
                    <div className="flex flex-col md:flex-row md:items-center justify-between">
                      <div className="flex-1">
                        <h3 className="font-semibold text-lg mb-2">
                          {ticket.eventTitle}
                        </h3>
                        <div className="space-y-1 text-sm text-text-secondary">
                          <div className="flex items-center">
                            <Calendar className="w-4 h-4 mr-2" />
                            {new Date(ticket.eventDate).toLocaleDateString()}
                          </div>
                          <div className="flex items-center">
                            <Clock className="w-4 h-4 mr-2" />
                            {ticket.eventTime}
                          </div>
                          <div className="flex items-center">
                            <MapPin className="w-4 h-4 mr-2" />
                            {ticket.eventLocation?.venue || "TBD"}
                          </div>
                        </div>
                        <div className="mt-2">
                          <span className="text-sm font-medium">
                            Ticket Type:{" "}
                          </span>
                          <span className="text-sm">{ticket.ticketType}</span>
                        </div>
                        {ticket.attendee_name && (
                          <div className="mt-1">
                            <span className="text-sm font-medium">
                              Attendee:{" "}
                            </span>
                            <span className="text-sm">
                              {ticket.attendee_name}
                            </span>
                          </div>
                        )}
                      </div>
                      <div className="mt-4 md:mt-0 md:ml-6">
                        <Button
                          onClick={() => downloadTicket(ticket.ticket_id)}
                          className="w-full md:w-auto"
                        >
                          <Download className="w-4 h-4 mr-2" />
                          Download PDF
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </motion.div>
          )}

          {/* Action Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="flex flex-col sm:flex-row gap-4 justify-center"
          >
            <Button
              variant="outline"
              onClick={() => router.push("/user-dashboard")}
              className="flex items-center"
            >
              View All Tickets
              <ArrowRight className="w-4 h-4 ml-2" />
            </Button>
            <Button
              onClick={() => router.push("/events")}
              className="flex items-center"
            >
              Browse More Events
              <ArrowRight className="w-4 h-4 ml-2" />
            </Button>
          </motion.div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
