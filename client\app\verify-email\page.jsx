"use client"

import { useEffect, useState } from "react"
import { useSearchPara<PERSON>, useRouter } from "next/navigation"
import { useAuth } from "@/context/auth-context"
import { Button } from "@/components/ui/button"
import { CheckCircle, XCircle, Loader2, Mail } from "lucide-react"

export default function VerifyEmailPage() {
  const [status, setStatus] = useState('verifying') // verifying, success, error
  const [message, setMessage] = useState('')
  const searchParams = useSearchParams()
  const router = useRouter()
  const { verifyEmail, resendVerificationEmail } = useAuth()
  const [resendLoading, setResendLoading] = useState(false)

  useEffect(() => {
    const token = searchParams.get('token')
    
    if (!token) {
      setStatus('error')
      setMessage('Invalid verification link')
      return
    }

    handleVerification(token)
  }, [searchParams])

  const handleVerification = async (token) => {
    try {
      const result = await verifyEmail(token)
      
      if (result.success) {
        setStatus('success')
        setMessage('Your email has been verified successfully!')
        
        // Redirect to login after 3 seconds
        setTimeout(() => {
          router.push('/')
        }, 3000)
      } else {
        setStatus('error')
        setMessage(result.message || 'Email verification failed')
      }
    } catch (error) {
      setStatus('error')
      setMessage('Email verification failed')
    }
  }

  const handleResendVerification = async () => {
    const email = prompt('Please enter your email address:')
    
    if (!email) return

    try {
      setResendLoading(true)
      await resendVerificationEmail(email)
    } catch (error) {
      console.error('Resend verification error:', error)
    } finally {
      setResendLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-zinc-950 flex items-center justify-center p-4">
      <div className="max-w-md w-full bg-zinc-900 rounded-lg p-8 text-center">
        {status === 'verifying' && (
          <>
            <Loader2 className="h-16 w-16 text-red-500 mx-auto mb-4 animate-spin" />
            <h1 className="text-2xl font-bold text-white mb-2">Verifying Email</h1>
            <p className="text-zinc-400">Please wait while we verify your email address...</p>
          </>
        )}

        {status === 'success' && (
          <>
            <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
            <h1 className="text-2xl font-bold text-white mb-2">Email Verified!</h1>
            <p className="text-zinc-400 mb-6">{message}</p>
            <p className="text-sm text-zinc-500">Redirecting you to the homepage...</p>
          </>
        )}

        {status === 'error' && (
          <>
            <XCircle className="h-16 w-16 text-red-500 mx-auto mb-4" />
            <h1 className="text-2xl font-bold text-white mb-2">Verification Failed</h1>
            <p className="text-zinc-400 mb-6">{message}</p>
            
            <div className="space-y-3">
              <Button
                onClick={handleResendVerification}
                disabled={resendLoading}
                className="w-full bg-red-600 hover:bg-red-700"
              >
                {resendLoading ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Sending...
                  </>
                ) : (
                  <>
                    <Mail className="h-4 w-4 mr-2" />
                    Resend Verification Email
                  </>
                )}
              </Button>
              
              <Button
                variant="outline"
                onClick={() => router.push('/')}
                className="w-full"
              >
                Back to Homepage
              </Button>
            </div>
          </>
        )}
      </div>
    </div>
  )
}
