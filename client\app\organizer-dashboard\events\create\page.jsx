"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { motion } from "framer-motion"
import DashboardLayout from "@/components/dashboard/dashboard-layout"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useToast } from "@/hooks/use-toast"
import { Calendar, Clock, MapPin, Upload, Plus, Trash2, Eye, Save, ArrowLeft } from "lucide-react"

export default function CreateEventPage() {
  const router = useRouter()
  const { toast } = useToast()
  const [activeTab, setActiveTab] = useState("details")
  const [eventData, setEventData] = useState({
    title: "",
    startDate: "",
    endDate: "",
    startTime: "",
    endTime: "",
    saleStartDate: "",
    saleEndDate: "",
    venueName: "",
    venueAddress: "",
    description: "",
    bannerImage: null,
    categories: [],
  })

  const [categories, setCategories] = useState([])

  const handleInputChange = (field, value) => {
    setEventData((prev) => ({ ...prev, [field]: value }))
  }

  const addCategory = () => {
    const newCategory = {
      id: Date.now(),
      name: "",
      type: "general",
      description: "",
      tickets: [],
    }
    setCategories((prev) => [...prev, newCategory])
  }

  const updateCategory = (categoryId, field, value) => {
    setCategories((prev) => prev.map((cat) => (cat.id === categoryId ? { ...cat, [field]: value } : cat)))
  }

  const removeCategory = (categoryId) => {
    setCategories((prev) => prev.filter((cat) => cat.id !== categoryId))
  }

  const addTicketType = (categoryId) => {
    const newTicket = {
      id: Date.now(),
      name: "",
      price: "",
      description: "",
      quantity: "",
      banner: null,
    }

    setCategories((prev) =>
      prev.map((cat) => (cat.id === categoryId ? { ...cat, tickets: [...cat.tickets, newTicket] } : cat)),
    )
  }

  const updateTicket = (categoryId, ticketId, field, value) => {
    setCategories((prev) =>
      prev.map((cat) =>
        cat.id === categoryId
          ? {
              ...cat,
              tickets: cat.tickets.map((ticket) => (ticket.id === ticketId ? { ...ticket, [field]: value } : ticket)),
            }
          : cat,
      ),
    )
  }

  const removeTicket = (categoryId, ticketId) => {
    setCategories((prev) =>
      prev.map((cat) =>
        cat.id === categoryId ? { ...cat, tickets: cat.tickets.filter((ticket) => ticket.id !== ticketId) } : cat,
      ),
    )
  }

  const handleSave = () => {
    toast({
      title: "Event Saved",
      description: "Your event has been saved as draft.",
      variant: "success",
    })
  }

  const handlePublish = () => {
    toast({
      title: "Event Published",
      description: "Your event is now live and accepting bookings!",
      variant: "success",
    })
    router.push("/dashboard/events")
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="sm" onClick={() => router.back()} className="text-zinc-400 hover:text-white">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            <div>
              <h1 className="text-2xl font-bold">Create New Event</h1>
              <p className="text-zinc-400">Set up your event details and ticket categories</p>
            </div>
          </div>

          <div className="flex gap-3">
            <Button variant="outline" onClick={handleSave} className="border-zinc-700">
              <Save className="h-4 w-4 mr-2" />
              Save Draft
            </Button>
            <Button onClick={handlePublish} className="bg-red-600 hover:bg-red-700">
              Publish Event
            </Button>
          </div>
        </div>

        {/* Main Content */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="bg-zinc-900">
            <TabsTrigger value="details">Event Details</TabsTrigger>
            <TabsTrigger value="tickets">Categories & Tickets</TabsTrigger>
            <TabsTrigger value="preview">Preview</TabsTrigger>
          </TabsList>

          {/* Event Details Tab */}
          <TabsContent value="details" className="space-y-6">
            <Card className="bg-zinc-900 border-zinc-800">
              <CardHeader>
                <CardTitle>Basic Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div>
                  <Label htmlFor="title">Event Title *</Label>
                  <Input
                    id="title"
                    value={eventData.title}
                    onChange={(e) => handleInputChange("title", e.target.value)}
                    placeholder="Enter event title"
                    className="bg-zinc-800 border-zinc-700"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <Label htmlFor="startDate">Start Date *</Label>
                    <Input
                      id="startDate"
                      type="date"
                      value={eventData.startDate}
                      onChange={(e) => handleInputChange("startDate", e.target.value)}
                      className="bg-zinc-800 border-zinc-700"
                    />
                  </div>
                  <div>
                    <Label htmlFor="endDate">End Date *</Label>
                    <Input
                      id="endDate"
                      type="date"
                      value={eventData.endDate}
                      onChange={(e) => handleInputChange("endDate", e.target.value)}
                      className="bg-zinc-800 border-zinc-700"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <Label htmlFor="startTime">Start Time *</Label>
                    <Input
                      id="startTime"
                      type="time"
                      value={eventData.startTime}
                      onChange={(e) => handleInputChange("startTime", e.target.value)}
                      className="bg-zinc-800 border-zinc-700"
                    />
                  </div>
                  <div>
                    <Label htmlFor="endTime">End Time *</Label>
                    <Input
                      id="endTime"
                      type="time"
                      value={eventData.endTime}
                      onChange={(e) => handleInputChange("endTime", e.target.value)}
                      className="bg-zinc-800 border-zinc-700"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <Label htmlFor="saleStartDate">Ticket Sale Start Date *</Label>
                    <Input
                      id="saleStartDate"
                      type="date"
                      value={eventData.saleStartDate}
                      onChange={(e) => handleInputChange("saleStartDate", e.target.value)}
                      className="bg-zinc-800 border-zinc-700"
                    />
                  </div>
                  <div>
                    <Label htmlFor="saleEndDate">Ticket Sale End Date *</Label>
                    <Input
                      id="saleEndDate"
                      type="date"
                      value={eventData.saleEndDate}
                      onChange={(e) => handleInputChange("saleEndDate", e.target.value)}
                      className="bg-zinc-800 border-zinc-700"
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="venueName">Venue Name *</Label>
                  <Input
                    id="venueName"
                    value={eventData.venueName}
                    onChange={(e) => handleInputChange("venueName", e.target.value)}
                    placeholder="Enter venue name"
                    className="bg-zinc-800 border-zinc-700"
                  />
                </div>

                <div>
                  <Label htmlFor="venueAddress">Venue Address *</Label>
                  <Input
                    id="venueAddress"
                    value={eventData.venueAddress}
                    onChange={(e) => handleInputChange("venueAddress", e.target.value)}
                    placeholder="Enter full venue address"
                    className="bg-zinc-800 border-zinc-700"
                  />
                </div>

                <div>
                  <Label htmlFor="description">Event Description *</Label>
                  <Textarea
                    id="description"
                    value={eventData.description}
                    onChange={(e) => handleInputChange("description", e.target.value)}
                    placeholder="Describe your event..."
                    rows={4}
                    className="bg-zinc-800 border-zinc-700"
                  />
                </div>

                <div>
                  <Label>Event Banner Image *</Label>
                  <div className="mt-2 border-2 border-dashed border-zinc-700 rounded-lg p-6 text-center hover:border-zinc-600 transition-colors">
                    <Upload className="h-8 w-8 text-zinc-400 mx-auto mb-2" />
                    <p className="text-zinc-400">Click to upload or drag and drop</p>
                    <p className="text-xs text-zinc-500 mt-1">PNG, JPG up to 10MB</p>
                    <input type="file" className="hidden" accept="image/*" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Categories & Tickets Tab */}
          <TabsContent value="tickets" className="space-y-6">
            <div className="flex justify-between items-center">
              <div>
                <h3 className="text-lg font-semibold">Ticket Categories</h3>
                <p className="text-zinc-400">Create different categories and ticket types for your event</p>
              </div>
              <Button onClick={addCategory} className="bg-red-600 hover:bg-red-700">
                <Plus className="h-4 w-4 mr-2" />
                Add Category
              </Button>
            </div>

            {categories.length === 0 ? (
              <Card className="bg-zinc-900 border-zinc-800">
                <CardContent className="flex flex-col items-center justify-center py-12">
                  <Calendar className="h-12 w-12 text-zinc-600 mb-4" />
                  <h3 className="text-lg font-semibold text-white mb-2">No categories yet</h3>
                  <p className="text-zinc-400 text-center mb-6">Create your first ticket category to get started</p>
                  <Button onClick={addCategory} className="bg-red-600 hover:bg-red-700">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Category
                  </Button>
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-6">
                {categories.map((category, categoryIndex) => (
                  <motion.div
                    key={category.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: categoryIndex * 0.1 }}
                  >
                    <Card className="bg-zinc-900 border-zinc-800">
                      <CardHeader className="flex flex-row items-center justify-between">
                        <CardTitle>Category {categoryIndex + 1}</CardTitle>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeCategory(category.id)}
                          className="text-red-400 hover:text-red-300"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div>
                            <Label>Category Name</Label>
                            <Input
                              value={category.name}
                              onChange={(e) => updateCategory(category.id, "name", e.target.value)}
                              placeholder="e.g., VIP, General Admission"
                              className="bg-zinc-800 border-zinc-700"
                            />
                          </div>
                          <div>
                            <Label>Category Type</Label>
                            <select
                              value={category.type}
                              onChange={(e) => updateCategory(category.id, "type", e.target.value)}
                              className="w-full h-10 px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-md text-white"
                            >
                              <option value="general">General</option>
                              <option value="vip">VIP</option>
                              <option value="premium">Premium</option>
                              <option value="student">Student</option>
                            </select>
                          </div>
                        </div>

                        <div>
                          <Label>Description</Label>
                          <Textarea
                            value={category.description}
                            onChange={(e) => updateCategory(category.id, "description", e.target.value)}
                            placeholder="Describe this category..."
                            className="bg-zinc-800 border-zinc-700"
                          />
                        </div>

                        {/* Tickets for this category */}
                        <div className="border-t border-zinc-800 pt-4">
                          <div className="flex justify-between items-center mb-4">
                            <h4 className="font-medium">Ticket Types</h4>
                            <Button
                              size="sm"
                              onClick={() => addTicketType(category.id)}
                              className="bg-zinc-700 hover:bg-zinc-600"
                            >
                              <Plus className="h-4 w-4 mr-2" />
                              Add Ticket
                            </Button>
                          </div>

                          {category.tickets.map((ticket, ticketIndex) => (
                            <div key={ticket.id} className="bg-zinc-800 rounded-lg p-4 mb-4">
                              <div className="flex justify-between items-center mb-4">
                                <h5 className="font-medium">Ticket {ticketIndex + 1}</h5>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => removeTicket(category.id, ticket.id)}
                                  className="text-red-400 hover:text-red-300"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </div>

                              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                  <Label>Ticket Name</Label>
                                  <Input
                                    value={ticket.name}
                                    onChange={(e) => updateTicket(category.id, ticket.id, "name", e.target.value)}
                                    placeholder="e.g., Early Bird, Regular"
                                    className="bg-zinc-700 border-zinc-600"
                                  />
                                </div>
                                <div>
                                  <Label>Price ($)</Label>
                                  <Input
                                    type="number"
                                    value={ticket.price}
                                    onChange={(e) => updateTicket(category.id, ticket.id, "price", e.target.value)}
                                    placeholder="0.00"
                                    className="bg-zinc-700 border-zinc-600"
                                  />
                                </div>
                                <div>
                                  <Label>Quantity Available</Label>
                                  <Input
                                    type="number"
                                    value={ticket.quantity}
                                    onChange={(e) => updateTicket(category.id, ticket.id, "quantity", e.target.value)}
                                    placeholder="100"
                                    className="bg-zinc-700 border-zinc-600"
                                  />
                                </div>
                                <div>
                                  <Label>Description</Label>
                                  <Input
                                    value={ticket.description}
                                    onChange={(e) =>
                                      updateTicket(category.id, ticket.id, "description", e.target.value)
                                    }
                                    placeholder="Ticket description"
                                    className="bg-zinc-700 border-zinc-600"
                                  />
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                ))}
              </div>
            )}
          </TabsContent>

          {/* Preview Tab */}
          <TabsContent value="preview" className="space-y-6">
            <Card className="bg-zinc-900 border-zinc-800">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Eye className="h-5 w-5" />
                  Event Preview
                </CardTitle>
                <p className="text-zinc-400">This is how your event will appear to customers</p>
              </CardHeader>
              <CardContent>
                <div className="bg-zinc-800 rounded-lg p-6 space-y-6">
                  {/* Event Header */}
                  <div className="text-center">
                    <div className="w-full h-64 bg-zinc-700 rounded-lg mb-4 flex items-center justify-center">
                      <Upload className="h-12 w-12 text-zinc-500" />
                    </div>
                    <h1 className="text-3xl font-bold mb-2">{eventData.title || "Event Title"}</h1>
                    <div className="flex items-center justify-center gap-6 text-zinc-400">
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4" />
                        <span>
                          {eventData.startDate ? new Date(eventData.startDate).toLocaleDateString() : "Date TBD"}
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Clock className="h-4 w-4" />
                        <span>{eventData.startTime || "Time TBD"}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <MapPin className="h-4 w-4" />
                        <span>{eventData.venueName || "Venue TBD"}</span>
                      </div>
                    </div>
                  </div>

                  {/* Event Description */}
                  <div>
                    <h3 className="text-lg font-semibold mb-2">About This Event</h3>
                    <p className="text-zinc-400">{eventData.description || "Event description will appear here..."}</p>
                  </div>

                  {/* Ticket Categories */}
                  {categories.length > 0 && (
                    <div>
                      <h3 className="text-lg font-semibold mb-4">Tickets</h3>
                      <div className="space-y-4">
                        {categories.map((category) => (
                          <div key={category.id} className="border border-zinc-700 rounded-lg p-4">
                            <h4 className="font-medium mb-2">{category.name || "Category Name"}</h4>
                            <p className="text-sm text-zinc-400 mb-3">
                              {category.description || "Category description"}
                            </p>
                            <div className="grid gap-2">
                              {category.tickets.map((ticket) => (
                                <div
                                  key={ticket.id}
                                  className="flex justify-between items-center p-2 bg-zinc-700 rounded"
                                >
                                  <div>
                                    <span className="font-medium">{ticket.name || "Ticket Name"}</span>
                                    <p className="text-xs text-zinc-400">{ticket.description}</p>
                                  </div>
                                  <div className="text-right">
                                    <div className="font-bold">${ticket.price || "0.00"}</div>
                                    <div className="text-xs text-zinc-400">{ticket.quantity || "0"} available</div>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  )
}
