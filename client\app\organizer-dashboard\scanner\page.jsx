"use client"

import { useState, useRef, useEffect } from "react"
import { motion } from "framer-motion"
import DashboardLayout from "@/components/dashboard/dashboard-layout"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { useToast } from "@/hooks/use-toast"
import { QrCode, Camera, CheckCircle, XCircle, Ticket } from "lucide-react"

// Mock ticket data for demonstration
const mockTicketData = {
  "TICKET-12345-VIP": {
    id: "TICKET-12345-VIP",
    eventTitle: "Summer Music Festival",
    attendeeName: "John Doe",
    attendeeEmail: "<EMAIL>",
    ticketType: "VIP",
    eventDate: "2024-07-15",
    eventLocation: "Central Park, New York",
    isVerified: false,
    purchaseDate: "2024-06-01",
  },
  "TICKET-67890-GA": {
    id: "TICKET-67890-GA",
    eventTitle: "Tech Conference 2024",
    attendeeName: "<PERSON>",
    attendeeEmail: "<EMAIL>",
    ticketType: "General Admission",
    eventDate: "2024-08-20",
    eventLocation: "Convention Center, San Francisco",
    isVerified: true,
    purchaseDate: "2024-07-10",
  },
}

const TicketDetails = ({ ticket, onVerify, onClose }) => {
  const { toast } = useToast()

  const handleVerify = () => {
    if (ticket.isVerified) {
      toast({
        title: "Already Verified",
        description: "This ticket has already been verified.",
        variant: "destructive",
      })
      return
    }

    onVerify(ticket.id)
    toast({
      title: "Ticket Verified",
      description: `${ticket.attendeeName}'s ticket has been verified successfully.`,
      variant: "success",
    })
  }

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      className="fixed inset-0 bg-black bg-opacity-80 flex items-center justify-center z-50 p-4"
    >
      <Card className="bg-zinc-900 border-zinc-800 max-w-md w-full">
        <CardHeader className="text-center">
          <div
            className={`mx-auto w-16 h-16 rounded-full flex items-center justify-center mb-4 ${
              ticket.isVerified ? "bg-green-900" : "bg-blue-900"
            }`}
          >
            {ticket.isVerified ? (
              <CheckCircle className="h-8 w-8 text-green-400" />
            ) : (
              <Ticket className="h-8 w-8 text-blue-400" />
            )}
          </div>
          <CardTitle className="text-xl">{ticket.isVerified ? "Already Verified" : "Ticket Found"}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-zinc-400">Event:</span>
              <span className="font-medium text-right">{ticket.eventTitle}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-zinc-400">Attendee:</span>
              <span className="font-medium text-right">{ticket.attendeeName}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-zinc-400">Email:</span>
              <span className="font-medium text-right text-sm">{ticket.attendeeEmail}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-zinc-400">Ticket Type:</span>
              <span className="font-medium text-right">{ticket.ticketType}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-zinc-400">Event Date:</span>
              <span className="font-medium text-right">{new Date(ticket.eventDate).toLocaleDateString()}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-zinc-400">Location:</span>
              <span className="font-medium text-right text-sm">{ticket.eventLocation}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-zinc-400">Status:</span>
              <span className={`font-medium text-right ${ticket.isVerified ? "text-green-400" : "text-blue-400"}`}>
                {ticket.isVerified ? "Verified" : "Not Verified"}
              </span>
            </div>
          </div>

          <div className="flex gap-3 pt-4">
            <Button variant="outline" onClick={onClose} className="flex-1 border-zinc-700">
              Close
            </Button>
            <Button
              onClick={handleVerify}
              className={`flex-1 ${
                ticket.isVerified ? "bg-zinc-700 hover:bg-zinc-600" : "bg-green-600 hover:bg-green-700"
              }`}
              disabled={ticket.isVerified}
            >
              {ticket.isVerified ? "Already Verified" : "Verify Ticket"}
            </Button>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}

export default function ScannerPage() {
  const { toast } = useToast()
  const [isScanning, setIsScanning] = useState(false)
  const [scannedTicket, setScannedTicket] = useState(null)
  const [manualCode, setManualCode] = useState("")
  const [verifiedTickets, setVerifiedTickets] = useState(new Set())
  const videoRef = useRef(null)
  const streamRef = useRef(null)

  const startCamera = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: { facingMode: "environment" },
      })
      streamRef.current = stream
      if (videoRef.current) {
        videoRef.current.srcObject = stream
      }
      setIsScanning(true)
      toast({
        title: "Camera Started",
        description: "Point your camera at a QR code to scan.",
        variant: "success",
      })
    } catch (error) {
      toast({
        title: "Camera Error",
        description: "Unable to access camera. Please check permissions.",
        variant: "destructive",
      })
    }
  }

  const stopCamera = () => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach((track) => track.stop())
      streamRef.current = null
    }
    setIsScanning(false)
  }

  const handleManualScan = () => {
    if (!manualCode.trim()) {
      toast({
        title: "Invalid Code",
        description: "Please enter a valid ticket code.",
        variant: "destructive",
      })
      return
    }

    const ticket = mockTicketData[manualCode]
    if (ticket) {
      setScannedTicket({
        ...ticket,
        isVerified: verifiedTickets.has(ticket.id),
      })
    } else {
      toast({
        title: "Ticket Not Found",
        description: "The entered ticket code is not valid.",
        variant: "destructive",
      })
    }
    setManualCode("")
  }

  const handleVerifyTicket = (ticketId) => {
    setVerifiedTickets((prev) => new Set([...prev, ticketId]))
    setScannedTicket((prev) => (prev ? { ...prev, isVerified: true } : null))
  }

  // Simulate QR code detection (in real implementation, you'd use a QR code library)
  const simulateQRScan = (code) => {
    const ticket = mockTicketData[code]
    if (ticket) {
      setScannedTicket({
        ...ticket,
        isVerified: verifiedTickets.has(ticket.id),
      })
      stopCamera()
    }
  }

  useEffect(() => {
    return () => {
      stopCamera()
    }
  }, [])

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Scanner Interface */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Camera Scanner */}
          <Card className="bg-zinc-900 border-zinc-800">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <QrCode className="h-5 w-5" />
                QR Code Scanner
              </CardTitle>
              <p className="text-zinc-400">Scan ticket QR codes to verify attendees</p>
            </CardHeader>
            <CardContent className="space-y-4">
              {!isScanning ? (
                <div className="aspect-video bg-zinc-800 rounded-lg flex flex-col items-center justify-center">
                  <Camera className="h-16 w-16 text-zinc-600 mb-4" />
                  <p className="text-zinc-400 mb-4">Camera not active</p>
                  <Button onClick={startCamera} className="bg-red-600 hover:bg-red-700">
                    <Camera className="h-4 w-4 mr-2" />
                    Start Camera
                  </Button>
                </div>
              ) : (
                <div className="relative aspect-video bg-zinc-800 rounded-lg overflow-hidden">
                  <video ref={videoRef} autoPlay playsInline className="w-full h-full object-cover" />
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="w-48 h-48 border-2 border-red-500 rounded-lg"></div>
                  </div>
                  <div className="absolute top-4 right-4">
                    <Button variant="outline" size="sm" onClick={stopCamera} className="bg-zinc-900/80 border-zinc-700">
                      Stop Camera
                    </Button>
                  </div>
                </div>
              )}

              {/* Demo buttons for testing */}
              {isScanning && (
                <div className="flex gap-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => simulateQRScan("TICKET-12345-VIP")}
                    className="border-zinc-700"
                  >
                    Scan VIP Ticket (Demo)
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => simulateQRScan("TICKET-67890-GA")}
                    className="border-zinc-700"
                  >
                    Scan GA Ticket (Demo)
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Manual Entry */}
          <Card className="bg-zinc-900 border-zinc-800">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Ticket className="h-5 w-5" />
                Manual Entry
              </CardTitle>
              <p className="text-zinc-400">Enter ticket code manually if QR scan fails</p>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">Ticket Code</label>
                <div className="flex gap-2">
                  <input
                    type="text"
                    value={manualCode}
                    onChange={(e) => setManualCode(e.target.value.toUpperCase())}
                    placeholder="TICKET-XXXXX-XXX"
                    className="flex-1 h-10 px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-md text-white placeholder-zinc-500"
                  />
                  <Button onClick={handleManualScan} className="bg-red-600 hover:bg-red-700">
                    Verify
                  </Button>
                </div>
              </div>

              <div className="bg-zinc-800 rounded-lg p-4">
                <h4 className="font-medium mb-2">Sample Codes for Testing:</h4>
                <div className="space-y-1 text-sm text-zinc-400">
                  <div>TICKET-12345-VIP (Not verified)</div>
                  <div>TICKET-67890-GA (Already verified)</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Scan Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="bg-zinc-900 border-zinc-800">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-zinc-400 text-sm">Total Scans Today</p>
                  <p className="text-2xl font-bold text-white">247</p>
                </div>
                <QrCode className="h-8 w-8 text-zinc-400" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-zinc-900 border-zinc-800">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-zinc-400 text-sm">Verified Tickets</p>
                  <p className="text-2xl font-bold text-green-400">235</p>
                </div>
                <CheckCircle className="h-8 w-8 text-green-400" />
              </div>
            </CardContent>
          </Card>

          <Card className="bg-zinc-900 border-zinc-800">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-zinc-400 text-sm">Invalid Scans</p>
                  <p className="text-2xl font-bold text-red-400">12</p>
                </div>
                <XCircle className="h-8 w-8 text-red-400" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Recent Scans */}
        <Card className="bg-zinc-900 border-zinc-800">
          <CardHeader>
            <CardTitle>Recent Scans</CardTitle>
            <p className="text-zinc-400">Latest ticket verifications</p>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {[
                { name: "Alice Johnson", ticket: "VIP", time: "2 minutes ago", status: "verified" },
                { name: "Bob Smith", ticket: "General", time: "5 minutes ago", status: "verified" },
                { name: "Carol Davis", ticket: "Premium", time: "8 minutes ago", status: "verified" },
                { name: "David Wilson", ticket: "Student", time: "12 minutes ago", status: "invalid" },
              ].map((scan, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-zinc-800 rounded-lg">
                  <div className="flex items-center gap-3">
                    <div
                      className={`w-2 h-2 rounded-full ${scan.status === "verified" ? "bg-green-400" : "bg-red-400"}`}
                    />
                    <div>
                      <p className="font-medium text-white">{scan.name}</p>
                      <p className="text-sm text-zinc-400">
                        {scan.ticket} • {scan.time}
                      </p>
                    </div>
                  </div>
                  <div
                    className={`px-2 py-1 rounded-full text-xs ${
                      scan.status === "verified" ? "bg-green-900 text-green-300" : "bg-red-900 text-red-300"
                    }`}
                  >
                    {scan.status}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Ticket Details Modal */}
      {scannedTicket && (
        <TicketDetails ticket={scannedTicket} onVerify={handleVerifyTicket} onClose={() => setScannedTicket(null)} />
      )}
    </DashboardLayout>
  )
}
