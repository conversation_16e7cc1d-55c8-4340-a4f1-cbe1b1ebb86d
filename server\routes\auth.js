const express = require('express');
const AuthController = require('../controllers/authController');
const { verifyTokenFromCookie } = require('../middleware/jwtCookieMiddleware');

const router = express.Router();

// Public routes
router.post('/register', AuthController.register);
router.post('/login', AuthController.login);
router.get('/verify-email', AuthController.verifyEmail);
router.post('/forgot-password', AuthController.forgotPassword);
router.post('/reset-password', AuthController.resetPassword);
router.post('/resend-verification', AuthController.resendVerificationEmail);

// OAuth routes
router.get('/oauth/:provider', AuthController.initiateOAuth);
router.get('/oauth/:provider/callback', AuthController.oauthCallback);
router.post('/oauth/sync', AuthController.syncOAuthUser);

// Protected routes - using JWT cookie middleware for email login sessions
router.get('/me', verifyTokenFromCookie, AuthController.getCurrentUser);
router.get('/validate-session', verifyTokenFromCookie, AuthController.validateSession);
router.post('/logout', verifyTokenFromCookie, AuthController.logout);
router.put('/update-profile', verifyTokenFromCookie, AuthController.updateProfile);
router.post('/change-password', verifyTokenFromCookie, AuthController.changePassword);

module.exports = router;
