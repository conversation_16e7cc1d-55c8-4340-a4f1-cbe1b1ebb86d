const jwt = require("jsonwebtoken");
const { PrismaClient } = require("@prisma/client");

const prisma = new PrismaClient();
const JWT_SECRET = process.env.JWT_SECRET || "fitfaat-secret-key";

async function testAuthStructure() {
  try {
    console.log("🧪 Testing Authentication Structure...\n");

    // Find an existing user to test with (preferably one with a profile)
    let existingUser = await prisma.masteraccounts.findFirst({
      where: {
        email_verified: true,
        role_type: "user",
        role_id: { not: null },
      },
      include: {
        users: true,
      },
    });

    if (!existingUser) {
      console.log(
        "❌ No verified users with profiles found. Looking for any verified user..."
      );

      // Try to find any verified user
      const anyUser = await prisma.masteraccounts.findFirst({
        where: {
          email_verified: true,
          role_type: "user",
        },
        include: {
          users: true,
        },
      });

      if (!anyUser) {
        console.log("❌ No verified users found in database at all");
        return;
      }

      console.log("✅ Found verified user without complete profile setup");
      // Use this user for testing
      existingUser = anyUser;
    }

    console.log("✅ Found existing user:", {
      accountId: existingUser.account_id,
      email: existingUser.email,
      roleType: existingUser.role_type,
      emailVerified: existingUser.email_verified,
      roleId: existingUser.role_id,
    });

    // Generate a token for this user
    const tokenData = {
      id: existingUser.account_id,
      email: existingUser.email,
      name: existingUser.users[0]
        ? `${existingUser.users[0].first_name} ${existingUser.users[0].last_name}`
        : "Test User",
      role: existingUser.role_type,
    };

    const token = jwt.sign(tokenData, JWT_SECRET, { expiresIn: "1h" });
    console.log("\n🔑 Generated test token:", token.substring(0, 50) + "...");

    // Simulate the middleware processing
    console.log("\n🔄 Simulating middleware processing...");

    const verified = jwt.verify(token, JWT_SECRET);

    // Get account details
    const account = await prisma.masteraccounts.findUnique({
      where: { account_id: verified.id },
      select: {
        account_id: true,
        email: true,
        role_type: true,
        email_verified: true,
        role_id: true,
      },
    });

    // Get profile data
    let profile = null;
    if (account.role_type === "user" && account.role_id) {
      profile = await prisma.users.findUnique({
        where: { user_id: account.role_id },
        select: {
          user_id: true,
          account_id: true,
          first_name: true,
          last_name: true,
          phone_number: true,
          profile_image: true,
          gender: true,
          dob: true,
          created_at: true,
          updated_at: true,
        },
      });
    }

    // Create the new user structure
    const userStructure = {
      accountId: account.account_id,
      email: account.email,
      roleType: account.role_type,
      emailVerified: account.email_verified,
      profile: profile,
      // Legacy fields for backward compatibility
      roleId: account.role_id,
      id: account.account_id,
      user_id: account.account_id,
      role: account.role_type,
      name: verified.name,
      iat: verified.iat,
      exp: verified.exp,
    };

    console.log("\n✨ New User Structure (req.user):");
    console.log(JSON.stringify(userStructure, null, 2));

    console.log("\n🎯 Key Features:");
    console.log("✅ accountId:", userStructure.accountId);
    console.log("✅ email:", userStructure.email);
    console.log("✅ roleType:", userStructure.roleType);
    console.log("✅ emailVerified:", userStructure.emailVerified);
    console.log(
      "✅ profile:",
      profile ? "Present" : "null (user needs profile setup)"
    );
    console.log("✅ Legacy fields maintained for backward compatibility");

    console.log("\n🚀 Authentication structure update completed successfully!");
  } catch (error) {
    console.error("❌ Test failed:", error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
testAuthStructure();
