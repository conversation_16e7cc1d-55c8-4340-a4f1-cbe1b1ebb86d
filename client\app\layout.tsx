import type React from "react"
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/context/cart-context"
import { AuthProvider } from "@/context/auth-context"
import { InterestedProvider } from "@/context/interested-context"
import { CheckoutProvider } from "@/context/checkout-context"
import { ThemeProvider } from "@/components/theme-provider"
import { Toaster } from "@/components/ui/sonner"
import { Inter } from "next/font/google"
import "./globals.css"

const inter = Inter({ subsets: ["latin"] })

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className="dark" suppressHydrationWarning>
      <body className={`${inter.className} bg-gray-950 text-white`}>
        <ThemeProvider attribute="class" defaultTheme="dark" enableSystem={false}>
          <AuthProvider>
            <CartProvider>
              <CheckoutProvider>
                <InterestedProvider>
                  {children}
                  <Toaster />
                </InterestedProvider>
              </CheckoutProvider>
            </CartProvider>
          </AuthProvider>
        </ThemeProvider>
      </body>
    </html>
  )
}

export const metadata = {
      generator: 'v0.dev'
    };
