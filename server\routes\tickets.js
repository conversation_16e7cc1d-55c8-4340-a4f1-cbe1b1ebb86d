const express = require("express");
const TicketController = require("../controllers/ticketController");
const { verifyTokenFromCookie } = require("../middleware/jwtCookieMiddleware");

const router = express.Router();
const ticketController = new TicketController();

// All ticket routes require authentication
router.use(verifyTokenFromCookie);

// Create tickets with complete workflow (order, tickets, QR codes, PDFs)
// POST /api/tickets/create
router.post("/create", ticketController.createTickets);

// Generate tickets from existing order (for two-stage purchase flow)
// POST /api/tickets/generate/:orderId
router.post("/generate/:orderId", ticketController.generateTicketsFromOrder);

// Get tickets by order ID
// GET /api/tickets/order/:orderId
router.get("/order/:orderId", ticketController.getTicketsByOrderId);

// Download ticket PDF
// GET /api/tickets/:ticketId/pdf
router.get("/:ticketId/pdf", ticketController.downloadTicketPDF);

// Get ticket details by QR code (for validation/scanning)
// GET /api/tickets/qr/:qrCode
router.get("/qr/:qrCode", ticketController.getTicketByQRCode);

// Validate/scan a ticket
// POST /api/tickets/:ticketId/validate
router.post("/:ticketId/validate", ticketController.validateTicket);

module.exports = router;
