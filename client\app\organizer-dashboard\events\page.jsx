"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { motion } from "framer-motion"
import DashboardLayout from "@/components/dashboard/dashboard-layout"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Tabs, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { useToast } from "@/hooks/use-toast"
import {
  Plus,
  Search,
  Calendar,
  MapPin,
  DollarSign,
  Users,
  MoreVertical,
  Edit,
  BarChart3,
  Download,
  Trash2,
  Eye,
} from "lucide-react"

// Mock events data
const mockEvents = [
  {
    id: 1,
    title: "Summer Music Festival",
    date: "2024-07-15",
    endDate: "2024-07-17",
    location: "Central Park, New York",
    image: "/placeholder.svg?height=200&width=300",
    status: "active",
    ticketsSold: 1250,
    totalTickets: 1500,
    revenue: 62500,
    attendees: 1180,
    categories: ["VIP", "General", "Student"],
  },
  {
    id: 2,
    title: "Tech Conference 2024",
    date: "2024-08-20",
    endDate: "2024-08-22",
    location: "Convention Center, San Francisco",
    image: "/placeholder.svg?height=200&width=300",
    status: "active",
    ticketsSold: 800,
    totalTickets: 1000,
    revenue: 40000,
    attendees: 750,
    categories: ["Early Bird", "Regular", "Premium"],
  },
  {
    id: 3,
    title: "Food & Wine Expo",
    date: "2024-06-10",
    endDate: "2024-06-12",
    location: "Exhibition Hall, Chicago",
    image: "/placeholder.svg?height=200&width=300",
    status: "completed",
    ticketsSold: 600,
    totalTickets: 600,
    revenue: 18000,
    attendees: 580,
    categories: ["Tasting", "Workshop", "VIP"],
  },
  {
    id: 4,
    title: "Art Gallery Opening",
    date: "2024-09-05",
    endDate: "2024-09-05",
    location: "Modern Art Museum, Los Angeles",
    image: "/placeholder.svg?height=200&width=300",
    status: "draft",
    ticketsSold: 0,
    totalTickets: 200,
    revenue: 0,
    attendees: 0,
    categories: ["General", "VIP"],
  },
]

const EventCard = ({ event, onManage, onViewSales, onExportAttendees, onCancel }) => {
  const { toast } = useToast()

  const getStatusColor = (status) => {
    switch (status) {
      case "active":
        return "bg-green-900 text-green-300"
      case "completed":
        return "bg-blue-900 text-blue-300"
      case "draft":
        return "bg-yellow-900 text-yellow-300"
      default:
        return "bg-zinc-700 text-zinc-300"
    }
  }

  const handleExport = () => {
    toast({
      title: "Export Started",
      description: `Attendees data for ${event.title} is being exported.`,
      variant: "success",
    })
    onExportAttendees(event.id)
  }

  const handleCancel = () => {
    toast({
      title: "Event Cancelled",
      description: `${event.title} has been cancelled.`,
      variant: "destructive",
    })
    onCancel(event.id)
  }

  return (
    <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} className="group">
      <Card className="bg-zinc-900 border-zinc-800 hover:border-zinc-700 transition-colors">
        <div className="relative h-48 overflow-hidden rounded-t-lg">
          <img
            src={event.image || "/placeholder.svg"}
            alt={event.title}
            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
          />
          <div className="absolute top-4 right-4">
            <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(event.status)}`}>
              {event.status}
            </span>
          </div>
        </div>

        <CardContent className="p-6">
          <div className="flex justify-between items-start mb-4">
            <div>
              <h3 className="text-lg font-bold text-white mb-2">{event.title}</h3>
              <div className="space-y-1 text-sm text-zinc-400">
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4" />
                  <span>
                    {new Date(event.date).toLocaleDateString()}
                    {event.endDate !== event.date && ` - ${new Date(event.endDate).toLocaleDateString()}`}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <MapPin className="h-4 w-4" />
                  <span>{event.location}</span>
                </div>
              </div>
            </div>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="text-zinc-400 hover:text-white">
                  <MoreVertical className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="bg-zinc-800 border-zinc-700">
                <DropdownMenuItem onClick={() => onManage(event.id)} className="hover:bg-zinc-700">
                  <Edit className="mr-2 h-4 w-4" />
                  Manage Event
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => onViewSales(event.id)} className="hover:bg-zinc-700">
                  <BarChart3 className="mr-2 h-4 w-4" />
                  View Sales
                </DropdownMenuItem>
                <DropdownMenuItem onClick={handleExport} className="hover:bg-zinc-700">
                  <Download className="mr-2 h-4 w-4" />
                  Export Attendees
                </DropdownMenuItem>
                <DropdownMenuItem onClick={handleCancel} className="hover:bg-zinc-700 text-red-400">
                  <Trash2 className="mr-2 h-4 w-4" />
                  Cancel Event
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          <div className="grid grid-cols-2 gap-4 mb-4">
            <div className="bg-zinc-800 rounded-lg p-3">
              <div className="flex items-center gap-2 text-zinc-400 text-sm mb-1">
                <Users className="h-4 w-4" />
                Tickets Sold
              </div>
              <div className="text-white font-bold">
                {event.ticketsSold}/{event.totalTickets}
              </div>
              <div className="w-full bg-zinc-700 rounded-full h-1 mt-2">
                <div
                  className="bg-red-600 h-1 rounded-full transition-all duration-500"
                  style={{ width: `${(event.ticketsSold / event.totalTickets) * 100}%` }}
                />
              </div>
            </div>

            <div className="bg-zinc-800 rounded-lg p-3">
              <div className="flex items-center gap-2 text-zinc-400 text-sm mb-1">
                <DollarSign className="h-4 w-4" />
                Revenue
              </div>
              <div className="text-white font-bold">${event.revenue.toLocaleString()}</div>
              <div className="text-zinc-400 text-xs mt-1">{event.attendees} attendees</div>
            </div>
          </div>

          <div className="flex gap-2">
            <Button className="flex-1 bg-red-600 hover:bg-red-700" onClick={() => onManage(event.id)}>
              <Edit className="h-4 w-4 mr-2" />
              Manage
            </Button>
            <Button
              variant="outline"
              className="border-zinc-700 hover:bg-zinc-800"
              onClick={() => onViewSales(event.id)}
            >
              <Eye className="h-4 w-4" />
            </Button>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}

export default function EventsPage() {
  const router = useRouter()
  const [searchTerm, setSearchTerm] = useState("")
  const [activeTab, setActiveTab] = useState("all")

  const filteredEvents = mockEvents.filter((event) => {
    const matchesSearch =
      event.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      event.location.toLowerCase().includes(searchTerm.toLowerCase())

    if (activeTab === "all") return matchesSearch
    return matchesSearch && event.status === activeTab
  })

  const handleManage = (eventId) => {
    router.push(`/dashboard/events/${eventId}/manage`)
  }

  const handleViewSales = (eventId) => {
    router.push(`/dashboard/events/${eventId}/sales`)
  }

  const handleExportAttendees = (eventId) => {
    // Export logic would go here
    console.log("Exporting attendees for event:", eventId)
  }

  const handleCancel = (eventId) => {
    // Cancel event logic would go here
    console.log("Cancelling event:", eventId)
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header Actions */}
        <div className="flex flex-col md:flex-row gap-4 justify-between">
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-zinc-400 h-4 w-4" />
            <Input
              placeholder="Search events..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 bg-zinc-900 border-zinc-700 text-white"
            />
          </div>
          <Button className="bg-red-600 hover:bg-red-700" onClick={() => router.push("/dashboard/events/create")}>
            <Plus className="h-4 w-4 mr-2" />
            Create Event
          </Button>
        </div>

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="bg-zinc-900">
            <TabsTrigger value="all">All Events ({mockEvents.length})</TabsTrigger>
            <TabsTrigger value="active">Active ({mockEvents.filter((e) => e.status === "active").length})</TabsTrigger>
            <TabsTrigger value="completed">
              Completed ({mockEvents.filter((e) => e.status === "completed").length})
            </TabsTrigger>
            <TabsTrigger value="draft">Draft ({mockEvents.filter((e) => e.status === "draft").length})</TabsTrigger>
          </TabsList>

          <TabsContent value={activeTab} className="mt-6">
            {filteredEvents.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredEvents.map((event) => (
                  <EventCard
                    key={event.id}
                    event={event}
                    onManage={handleManage}
                    onViewSales={handleViewSales}
                    onExportAttendees={handleExportAttendees}
                    onCancel={handleCancel}
                  />
                ))}
              </div>
            ) : (
              <Card className="bg-zinc-900 border-zinc-800">
                <CardContent className="flex flex-col items-center justify-center py-12">
                  <Calendar className="h-12 w-12 text-zinc-600 mb-4" />
                  <h3 className="text-lg font-semibold text-white mb-2">No events found</h3>
                  <p className="text-zinc-400 text-center mb-6">
                    {searchTerm ? "No events match your search criteria." : "You haven't created any events yet."}
                  </p>
                  <Button
                    className="bg-red-600 hover:bg-red-700"
                    onClick={() => router.push("/dashboard/events/create")}
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Create Your First Event
                  </Button>
                </CardContent>
              </Card>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  )
}
