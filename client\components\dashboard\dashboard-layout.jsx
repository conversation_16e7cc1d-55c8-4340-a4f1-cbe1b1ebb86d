"use client"

import { useState } from "react"
import { useRouter, usePathname } from "next/navigation"
import { motion, AnimatePresence } from "framer-motion"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Button } from "@/components/ui/button"
import { useAuth } from "@/context/auth-context"
import {
  BarChart3,
  Calendar,
  TrendingUp,
  Users,
  QrCode,
  Settings,
  LogOut,
  User,
  ChevronDown,
  Menu,
  X,
} from "lucide-react"
import { useMediaQuery } from "@/hooks/use-media-query"

const sidebarItems = [
  { id: "overview", label: "Sales Overview", icon: BarChart3, href: "/dashboard" },
  { id: "events", label: "Events", icon: Calendar, href: "/dashboard/events" },
  { id: "sales", label: "Sales by Events", icon: TrendingUp, href: "/dashboard/sales" },
  { id: "attendees", label: "Attendees", icon: Users, href: "/dashboard/attendees" },
  { id: "scanner", label: "Scanner", icon: QrCode, href: "/dashboard/scanner" },
  { id: "settings", label: "Settings", icon: Settings, href: "/dashboard/settings" },
]

export default function DashboardLayout({ children }) {
  const router = useRouter()
  const pathname = usePathname()
  const { user, logout } = useAuth()
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const isMobile = useMediaQuery("(max-width: 768px)")

  const handleLogout = () => {
    logout()
    router.push("/")
  }

  const currentPath = pathname.split("/").pop() || "dashboard"
  const activeItem = sidebarItems.find(
    (item) => item.href === pathname || (pathname === "/dashboard" && item.id === "overview"),
  )

  return (
    <div className="min-h-screen bg-zinc-950 text-white">
      {/* Top Navigation */}
      <header className="bg-zinc-900 border-b border-zinc-800 px-4 py-3 flex items-center justify-between">
        <div className="flex items-center gap-4">
          {isMobile && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setSidebarOpen(!sidebarOpen)}
              className="text-white hover:bg-zinc-800"
            >
              {sidebarOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
            </Button>
          )}
          <h1 className="text-xl font-bold">Event Management</h1>
        </div>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="flex items-center gap-3 hover:bg-zinc-800">
              <Avatar className="h-8 w-8">
                <AvatarImage src={user?.avatar || "/placeholder.svg?height=32&width=32"} alt={user?.name} />
                <AvatarFallback>{user?.name?.charAt(0) || "U"}</AvatarFallback>
              </Avatar>
              <div className="text-left hidden md:block">
                <p className="text-sm font-medium">{user?.name || "Organizer"}</p>
                <p className="text-xs text-zinc-400">Event Organizer</p>
              </div>
              <ChevronDown className="h-4 w-4 text-zinc-400" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-56 bg-zinc-800 border border-zinc-700" align="end">
            <DropdownMenuItem
              className="cursor-pointer flex items-center hover:bg-zinc-700"
              onClick={() => router.push("/dashboard/settings")}
            >
              <User className="mr-2 h-4 w-4" />
              <span>Profile</span>
            </DropdownMenuItem>
            <DropdownMenuSeparator className="bg-zinc-700" />
            <DropdownMenuItem
              className="cursor-pointer flex items-center text-red-500 focus:text-red-500 hover:bg-zinc-700"
              onClick={handleLogout}
            >
              <LogOut className="mr-2 h-4 w-4" />
              <span>Logout</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </header>

      <div className="flex">
        {/* Sidebar */}
        <AnimatePresence>
          {(!isMobile || sidebarOpen) && (
            <motion.aside
              initial={isMobile ? { x: -280 } : false}
              animate={{ x: 0 }}
              exit={{ x: -280 }}
              transition={{ duration: 0.3, ease: "easeInOut" }}
              className={`${
                isMobile
                  ? "fixed inset-y-0 left-0 z-50 w-72 bg-zinc-900 border-r border-zinc-800"
                  : "w-72 bg-zinc-900 border-r border-zinc-800 min-h-screen"
              }`}
            >
              <div className="p-6">
                <nav className="space-y-2">
                  {sidebarItems.map((item) => {
                    const Icon = item.icon
                    const isActive = item.href === pathname || (pathname === "/dashboard" && item.id === "overview")

                    return (
                      <Button
                        key={item.id}
                        variant="ghost"
                        className={`w-full justify-start gap-3 h-12 ${
                          isActive
                            ? "bg-red-600 text-white hover:bg-red-700"
                            : "text-zinc-300 hover:bg-zinc-800 hover:text-white"
                        }`}
                        onClick={() => {
                          router.push(item.href)
                          if (isMobile) setSidebarOpen(false)
                        }}
                      >
                        <Icon className="h-5 w-5" />
                        {item.label}
                      </Button>
                    )
                  })}
                </nav>

                <div className="mt-8 pt-8 border-t border-zinc-800">
                  <Button
                    variant="ghost"
                    className="w-full justify-start gap-3 h-12 text-red-400 hover:bg-red-900/20 hover:text-red-300"
                    onClick={handleLogout}
                  >
                    <LogOut className="h-5 w-5" />
                    Logout
                  </Button>
                </div>
              </div>
            </motion.aside>
          )}
        </AnimatePresence>

        {/* Main Content */}
        <main className="flex-1 p-6">
          <div className="mb-6">
            <h2 className="text-2xl font-bold">{activeItem?.label || "Dashboard"}</h2>
            <p className="text-zinc-400 mt-1">Manage your events and track performance</p>
          </div>
          {children}
        </main>
      </div>

      {/* Mobile Overlay */}
      {isMobile && sidebarOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-40" onClick={() => setSidebarOpen(false)} />
      )}
    </div>
  )
}
