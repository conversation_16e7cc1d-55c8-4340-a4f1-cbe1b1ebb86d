"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/has-tostringtag@1.0.2";
exports.ids = ["vendor-chunks/has-tostringtag@1.0.2"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/has-tostringtag@1.0.2/node_modules/has-tostringtag/shams.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/.pnpm/has-tostringtag@1.0.2/node_modules/has-tostringtag/shams.js ***!
  \****************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar hasSymbols = __webpack_require__(/*! has-symbols/shams */ \"(ssr)/./node_modules/.pnpm/has-symbols@1.1.0/node_modules/has-symbols/shams.js\");\n\n/** @type {import('.')} */\nmodule.exports = function hasToStringTagShams() {\n\treturn hasSymbols() && !!Symbol.toStringTag;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vaGFzLXRvc3RyaW5ndGFnQDEuMC4yL25vZGVfbW9kdWxlcy9oYXMtdG9zdHJpbmd0YWcvc2hhbXMuanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWIsaUJBQWlCLG1CQUFPLENBQUMseUdBQW1COztBQUU1QyxXQUFXLGFBQWE7QUFDeEI7QUFDQTtBQUNBIiwic291cmNlcyI6WyJEOlxcUHJvamVjdCBmb3IgQ2xpZW50c1xcQ291bnRlckJEXFxDb3VudGVyc0JEXFxjbGllbnRcXG5vZGVfbW9kdWxlc1xcLnBucG1cXGhhcy10b3N0cmluZ3RhZ0AxLjAuMlxcbm9kZV9tb2R1bGVzXFxoYXMtdG9zdHJpbmd0YWdcXHNoYW1zLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxudmFyIGhhc1N5bWJvbHMgPSByZXF1aXJlKCdoYXMtc3ltYm9scy9zaGFtcycpO1xuXG4vKiogQHR5cGUge2ltcG9ydCgnLicpfSAqL1xubW9kdWxlLmV4cG9ydHMgPSBmdW5jdGlvbiBoYXNUb1N0cmluZ1RhZ1NoYW1zKCkge1xuXHRyZXR1cm4gaGFzU3ltYm9scygpICYmICEhU3ltYm9sLnRvU3RyaW5nVGFnO1xufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/has-tostringtag@1.0.2/node_modules/has-tostringtag/shams.js\n");

/***/ })

};
;