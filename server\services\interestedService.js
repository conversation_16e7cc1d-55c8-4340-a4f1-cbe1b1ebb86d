const prismaService = require("../lib/prisma");

class InterestedService {
  static instance = null;

  constructor() {
    this.prisma = prismaService.getInstance();
  }

  static getInstance() {
    if (!InterestedService.instance) {
      InterestedService.instance = new InterestedService();
    }
    return InterestedService.instance;
  }

  // Helper method to get user_id from account_id
  async getUserIdFromAccountId(accountId) {
    try {
      const user = await this.prisma.users.findUnique({
        where: {
          account_id: accountId,
        },
        select: {
          user_id: true,
        },
      });

      if (!user) {
        throw new Error(
          "User profile not found. Please complete your profile setup."
        );
      }

      return user.user_id;
    } catch (error) {
      console.error("Error getting user ID from account ID:", error);
      throw new Error("Failed to get user information");
    }
  }

  // Get all interested events for a user
  async getUserInterestedEvents(accountId) {
    try {
      const userId = await this.getUserIdFromAccountId(accountId);

      const interestedEvents = await this.prisma.wishlists.findMany({
        where: {
          user_id: userId,
        },
        include: {
          events: {
            include: {
              genres: true,
              locations: true,
              organizers: {
                select: {
                  organizer_id: true,
                  organization_name: true,
                  phone_number: true,
                },
              },
              eventartists: {
                include: {
                  artists: true,
                },
              },
              tickettypes: {
                select: {
                  ticket_type_id: true,
                  name: true,
                  price: true,
                  quantity_available: true,
                  description: true,
                },
              },
            },
          },
        },
        orderBy: {
          created_at: "desc",
        },
      });

      // Format the response to match the expected event structure
      const formattedEvents = interestedEvents.map((wishlistItem) => {
        const event = wishlistItem.events;
        return this.formatEventResponse(event);
      });

      return {
        events: formattedEvents,
        total: formattedEvents.length,
      };
    } catch (error) {
      console.error("Error fetching user interested events:", error);
      throw new Error("Failed to fetch interested events");
    }
  }

  // Add event to user's interested list
  async addToInterested(accountId, eventId) {
    try {
      const userId = await this.getUserIdFromAccountId(accountId);

      // Check if event exists
      const event = await this.prisma.events.findUnique({
        where: {
          event_id: parseInt(eventId),
        },
      });

      if (!event) {
        throw new Error("Event not found");
      }

      // Check if already in interested list
      const existingWishlist = await this.prisma.wishlists.findUnique({
        where: {
          user_id_event_id: {
            user_id: userId,
            event_id: parseInt(eventId),
          },
        },
      });

      if (existingWishlist) {
        throw new Error("Event is already in your interested list");
      }

      // Add to interested list
      const wishlistItem = await this.prisma.wishlists.create({
        data: {
          user_id: userId,
          event_id: parseInt(eventId),
        },
        include: {
          events: {
            include: {
              genres: true,
              locations: true,
              organizers: {
                select: {
                  organizer_id: true,
                  organization_name: true,
                  phone_number: true,
                },
              },
              eventartists: {
                include: {
                  artists: true,
                },
              },
              tickettypes: {
                select: {
                  ticket_type_id: true,
                  name: true,
                  price: true,
                  quantity_available: true,
                  description: true,
                },
              },
            },
          },
        },
      });

      return {
        success: true,
        event: this.formatEventResponse(wishlistItem.events),
        message: "Event added to interested list successfully",
      };
    } catch (error) {
      console.error("Error adding event to interested:", error);
      throw new Error(
        error.message || "Failed to add event to interested list"
      );
    }
  }

  // Remove event from user's interested list
  async removeFromInterested(accountId, eventId) {
    try {
      const userId = await this.getUserIdFromAccountId(accountId);

      // Check if item exists in wishlist
      const existingWishlist = await this.prisma.wishlists.findUnique({
        where: {
          user_id_event_id: {
            user_id: userId,
            event_id: parseInt(eventId),
          },
        },
      });

      if (!existingWishlist) {
        throw new Error("Event is not in your interested list");
      }

      // Remove from interested list
      await this.prisma.wishlists.delete({
        where: {
          user_id_event_id: {
            user_id: userId,
            event_id: parseInt(eventId),
          },
        },
      });

      return {
        success: true,
        message: "Event removed from interested list successfully",
      };
    } catch (error) {
      console.error("Error removing event from interested:", error);
      throw new Error(
        error.message || "Failed to remove event from interested list"
      );
    }
  }

  // Check if event is in user's interested list
  async isEventInInterested(accountId, eventId) {
    try {
      const userId = await this.getUserIdFromAccountId(accountId);

      const wishlistItem = await this.prisma.wishlists.findUnique({
        where: {
          user_id_event_id: {
            user_id: userId,
            event_id: parseInt(eventId),
          },
        },
      });

      return !!wishlistItem;
    } catch (error) {
      console.error("Error checking if event is in interested:", error);
      throw new Error("Failed to check interested status");
    }
  }

  // Format event response to match frontend expectations
  formatEventResponse(event) {
    if (!event) return null;

    // Get minimum ticket price
    const minPrice =
      event.tickettypes && event.tickettypes.length > 0
        ? Math.min(
            ...event.tickettypes.map((ticket) => parseFloat(ticket.price))
          )
        : 0;

    return {
      id: event.event_id,
      title: event.title,
      description: event.description,
      startDate: event.start_date,
      endDate: event.end_date,
      startTime: event.start_time,
      image: event.banner_image,
      venue: event.venue_name,
      location: event.locations
        ? {
            id: event.locations.location_id,
            city: event.locations.city,
            venue: event.locations.venue_name,
            address: event.locations.address,
            mapLink: event.locations.map_link,
          }
        : null,
      genre: event.genres
        ? {
            id: event.genres.genre_id,
            name: event.genres.name,
            icon: event.genres.icon,
          }
        : null,
      organizer: event.organizers
        ? {
            id: event.organizers.organizer_id,
            name: event.organizers.organization_name,
            phone: event.organizers.phone_number,
          }
        : null,
      artists: event.eventartists
        ? event.eventartists.map((ea) => ({
            id: ea.artists.artist_id,
            name: ea.artists.name,
            bio: ea.artists.bio,
            image: ea.artists.image,
          }))
        : [],
      tickets: event.tickettypes
        ? event.tickettypes.map((ticket) => ({
            id: ticket.ticket_type_id,
            name: ticket.name,
            price: parseFloat(ticket.price),
            available: ticket.quantity_available,
            description: ticket.description,
          }))
        : [],
      price: minPrice,
      status: event.status,
      policy: event.event_policy,
      ticketsSaleStart: event.tickets_sale_start,
      ticketsSaleEnd: event.tickets_sale_end,
      createdAt: event.created_at,
      updatedAt: event.updated_at,
    };
  }
}

module.exports = InterestedService;
