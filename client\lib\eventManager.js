import { eventsAPI } from './api';
import EventFilter from './eventFilter';

/**
 * EventManager - OOP class for managing event operations
 * Implements Singleton pattern and Observer pattern for state management
 * Now uses frontend-based filtering for better performance
 */
class EventManager {
  constructor() {
    if (EventManager.instance) {
      return EventManager.instance;
    }

    this.allEvents = []; // Store all events from API
    this.genres = [];
    this.locations = [];
    this.eventFilter = new EventFilter(); // Frontend filtering utility
    this.loading = false;
    this.error = null;
    this.observers = [];
    this.dataLoaded = false; // Track if initial data has been loaded

    EventManager.instance = this;
  }

  // Observer pattern implementation
  subscribe(observer) {
    this.observers.push(observer);
  }

  unsubscribe(observer) {
    this.observers = this.observers.filter(obs => obs !== observer);
  }

  notify(data) {
    this.observers.forEach(observer => observer(data));
  }

  // Set loading state
  setLoading(loading) {
    this.loading = loading;
    this.notify({ type: 'LOADING_CHANGED', loading });
  }

  // Set error state
  setError(error) {
    this.error = error;
    this.notify({ type: 'ERROR_CHANGED', error });
  }

  // Set all events (from API)
  setAllEvents(events) {
    this.allEvents = events;
    this.eventFilter.setEvents(events);
    this.dataLoaded = true;
    this.notify({ type: 'EVENTS_CHANGED', events: this.eventFilter.getPaginatedEvents() });
  }

  // Set genres
  setGenres(genres) {
    this.genres = genres;
    this.notify({ type: 'GENRES_CHANGED', genres });
  }

  // Set locations
  setLocations(locations) {
    this.locations = locations;
    this.notify({ type: 'LOCATIONS_CHANGED', locations });
  }

  // Update filters (frontend-based)
  updateFilters(newFilters) {
    this.eventFilter.updateFilters(newFilters);
    this.notify({
      type: 'FILTERS_CHANGED',
      filters: this.eventFilter.getFilters(),
      events: this.eventFilter.getPaginatedEvents()
    });
  }

  // Update pagination
  updatePagination(newPagination) {
    this.eventFilter.updatePagination(newPagination);
    this.notify({
      type: 'PAGINATION_CHANGED',
      pagination: this.eventFilter.getPagination(),
      events: this.eventFilter.getPaginatedEvents()
    });
  }

  // Load all events from API (only once)
  async loadAllEvents() {
    try {
      this.setLoading(true);
      this.setError(null);

      const response = await eventsAPI.getAllEvents();

      if (response.success) {
        this.setAllEvents(response.data.events);
        return this.eventFilter.getPaginatedEvents();
      } else {
        throw new Error(response.message || 'Failed to fetch events');
      }
    } catch (error) {
      console.error('Error fetching events:', error);
      this.setError(error.message);
      throw error;
    } finally {
      this.setLoading(false);
    }
  }

  // Get events with current filters (frontend-based)
  getEvents() {
    if (!this.dataLoaded) {
      // If data not loaded yet, load it first
      return this.loadAllEvents();
    }

    // Return filtered and paginated events
    return Promise.resolve(this.eventFilter.getPaginatedEvents());
  }

  // Filter by status (frontend-based)
  filterByStatus(status) {
    this.updateFilters({ status });
    return this.eventFilter.getPaginatedEvents();
  }

  // Search events (frontend-based)
  searchEvents(query) {
    this.updateFilters({ search: query });
    return this.eventFilter.getPaginatedEvents();
  }

  // Get event by ID
  async getEventById(id) {
    try {
      this.setLoading(true);
      this.setError(null);

      const response = await eventsAPI.getEventById(id);

      if (response.success) {
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to fetch event');
      }
    } catch (error) {
      console.error('Error fetching event:', error);
      this.setError(error.message);
      throw error;
    } finally {
      this.setLoading(false);
    }
  }

  // Load genres
  async loadGenres() {
    try {
      const response = await eventsAPI.getAllGenres();

      if (response.success) {
        this.setGenres(response.data);
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to fetch genres');
      }
    } catch (error) {
      console.error('Error fetching genres:', error);
      this.setError(error.message);
      throw error;
    }
  }

  // Load locations
  async loadLocations() {
    try {
      const response = await eventsAPI.getAllLocations();

      if (response.success) {
        this.setLocations(response.data);
        return response.data;
      } else {
        throw new Error(response.message || 'Failed to fetch locations');
      }
    } catch (error) {
      console.error('Error fetching locations:', error);
      this.setError(error.message);
      throw error;
    }
  }

  // Filter events by genre
  filterByGenre(genreId) {
    const currentGenres = this.eventFilter.getFilters().genreIds;
    const newGenres = currentGenres.includes(genreId)
      ? currentGenres.filter(id => id !== genreId)
      : [...currentGenres, genreId];

    this.updateFilters({ genreIds: newGenres });
    return this.eventFilter.getPaginatedEvents();
  }

  // Filter events by location
  filterByLocation(locationId) {
    const currentLocations = this.eventFilter.getFilters().locationIds;
    const newLocations = currentLocations.includes(locationId)
      ? currentLocations.filter(id => id !== locationId)
      : [...currentLocations, locationId];

    this.updateFilters({ locationIds: newLocations });
    return this.eventFilter.getPaginatedEvents();
  }

  // Change page
  changePage(page) {
    this.updatePagination({ page });
    return this.eventFilter.getPaginatedEvents();
  }

  // Change items per page
  changeItemsPerPage(limit) {
    this.updatePagination({ limit, page: 1 });
    return this.eventFilter.getPaginatedEvents();
  }

  // Reset filters
  resetFilters() {
    this.eventFilter.resetFilters();
    this.notify({
      type: 'FILTERS_CHANGED',
      filters: this.eventFilter.getFilters(),
      events: this.eventFilter.getPaginatedEvents()
    });
    return this.eventFilter.getPaginatedEvents();
  }

  // Get current state
  getState() {
    return {
      events: this.eventFilter.getPaginatedEvents(),
      allEvents: this.allEvents,
      genres: this.genres,
      locations: this.locations,
      filters: this.eventFilter.getFilters(),
      pagination: this.eventFilter.getPagination(),
      loading: this.loading,
      error: this.error,
      dataLoaded: this.dataLoaded,
      eventCounts: this.dataLoaded ? this.eventFilter.getEventCountsByStatus() : { live: 0, upcoming: 0, past: 0 }
    };
  }

  // Get event counts by status
  getEventCountsByStatus() {
    return this.eventFilter.getEventCountsByStatus();
  }

  // Sort events
  sortEvents(sortBy, sortOrder = 'asc') {
    this.updateFilters({ sortBy, sortOrder });
    return this.eventFilter.getPaginatedEvents();
  }
}

// Export singleton instance
export default new EventManager();
