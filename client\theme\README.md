# Color System

This directory contains the color system for the application. The colors are defined in a central location and can be easily updated to maintain consistency across the entire application.

## Structure

- `colors.js`: Contains all color definitions
- `index.js`: Provides utility functions for working with colors

## Usage

### In Tailwind CSS

Colors can be accessed via Tailwind classes:

\`\`\`jsx
<button className="bg-primary-600 text-white hover:bg-primary-700">
  Primary Button
</button>

<div className="bg-background-50 text-text-secondary">
  Content
</div>
\`\`\`

### In JavaScript/TypeScript

Colors can be imported and used directly:

\`\`\`jsx
import { colors } from '@/theme'

function MyComponent() {
  const primaryColor = colors.primary[600]
  
  return (
    <div style={{ backgroundColor: primaryColor }}>
      Content
    </div>
  )
}
\`\`\`

### Utility Functions

The theme system provides utility functions:

\`\`\`jsx
import { getColor, getCssVar } from '@/theme'

// Get a color value
const primaryColor = getColor('primary.600') // '#e11d48'

// Get a CSS variable reference
const cssVar = getCssVar('primary.600') // 'var(--primary-600)'
\`\`\`

## Color Categories

- **Primary**: Main brand colors (red)
- **Background**: Background colors for different UI elements
- **Gray**: Grayscale colors
- **Accent**: Additional colors for accents and highlights
- **Status**: Colors for status indicators (success, error, etc.)
- **Text**: Text colors
- **Border**: Border colors

## Extending

To add new colors, edit the `colors.js` file and add your new color definitions.
