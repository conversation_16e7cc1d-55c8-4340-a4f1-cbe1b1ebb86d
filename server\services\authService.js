const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { PrismaClient } = require('@prisma/client');
const { createClient } = require('@supabase/supabase-js');
const { v4: uuidv4 } = require('uuid');

const prisma = new PrismaClient();

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const supabase = createClient(supabaseUrl, supabaseServiceKey);

class AuthService {
  static instance = null;

  static getInstance() {
    if (!AuthService.instance) {
      AuthService.instance = new AuthService();
    }
    return AuthService.instance;
  }

  // Generate JWT tokens
  generateTokens(payload) {
    const accessToken = jwt.sign(payload, process.env.JWT_SECRET, {
      expiresIn: process.env.JWT_EXPIRES_IN || '15m'
    });

    const refreshToken = jwt.sign(payload, process.env.JWT_REFRESH_SECRET, {
      expiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d'
    });

    return { accessToken, refreshToken };
  }

  // Verify JWT token
  verifyToken(token, isRefresh = false) {
    const secret = isRefresh ? process.env.JWT_REFRESH_SECRET : process.env.JWT_SECRET;
    return jwt.verify(token, secret);
  }

  // Hash password
  async hashPassword(password) {
    const saltRounds = 12;
    return await bcrypt.hash(password, saltRounds);
  }

  // Compare password
  async comparePassword(password, hashedPassword) {
    return await bcrypt.compare(password, hashedPassword);
  }

  // Register with email/password
  async registerWithEmail(email, password, firstName, lastName, phoneNumber = '') {
    try {
      // Check if user already exists
      const existingAccount = await prisma.masteraccounts.findUnique({
        where: { email }
      });

      if (existingAccount) {
        throw new Error('User already exists with this email');
      }

      // Hash password
      const hashedPassword = await this.hashPassword(password);

      // Generate verification token
      const verificationToken = uuidv4();

      // Create account in masteraccounts table
      const account = await prisma.masteraccounts.create({
        data: {
          email,
          password_hash: hashedPassword,
          role_type: 'user',
          email_verified: false,
          verification_token: verificationToken,
          created_at: new Date(),
          updated_at: new Date()
        }
      });

      // Create user profile in users table
      const userProfile = await prisma.users.create({
        data: {
          account_id: account.account_id,
          first_name: firstName,
          last_name: lastName,
          phone_number: phoneNumber,
          created_at: new Date(),
          updated_at: new Date()
        }
      });

      // Update masteraccounts with role_id
      const updatedAccount = await prisma.masteraccounts.update({
        where: { account_id: account.account_id },
        data: {
          role_id: userProfile.user_id,
          updated_at: new Date()
        }
      });

      return {
        account: updatedAccount,
        userDetails: userProfile,
        verificationToken
      };

    } catch (error) {
      console.error('Registration error:', error);
      throw error;
    }
  }

  // Login with email/password
  async loginWithEmail(email, password) {
    try {
      // Find account
      const account = await prisma.masteraccounts.findUnique({
        where: { email },
        include: {
          users: true
        }
      });

      if (!account) {
        throw new Error('Invalid email or password');
      }

      // Check password
      const isValidPassword = await this.comparePassword(password, account.password_hash);
      if (!isValidPassword) {
        throw new Error('Invalid email or password');
      }

      // Check if email is verified
      if (account.email_verified === false || account.email_verified === 0) {
        throw new Error('Please verify your email before logging in');
      }

      // Generate tokens
      const tokenPayload = {
        accountId: account.account_id,
        email: account.email,
        roleType: account.role_type
      };

      const tokens = this.generateTokens(tokenPayload);

      // Update last login
      await prisma.masteraccounts.update({
        where: { account_id: account.account_id },
        data: {
          last_login: new Date(),
          updated_at: new Date()
        }
      });

      return {
        account,
        userDetails: account.users[0] || null,
        ...tokens
      };

    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  }

  // Verify email
  async verifyEmail(token) {
    try {
      const account = await prisma.masteraccounts.findFirst({
        where: { verification_token: token }
      });

      if (!account) {
        throw new Error('Invalid verification token');
      }

      // Update account as verified
      await prisma.masteraccounts.update({
        where: { account_id: account.account_id },
        data: {
          email_verified: true,
          verification_token: null,
          updated_at: new Date()
        }
      });

      return { success: true, message: 'Email verified successfully' };

    } catch (error) {
      console.error('Email verification error:', error);
      throw error;
    }
  }

  // Handle OAuth callback
  async handleOAuthCallback(provider, oauthData) {
    try {
      const { email, name, sub } = oauthData;

      // Check if account exists
      let account = await prisma.masteraccounts.findUnique({
        where: { email },
        include: { users: true }
      });

      if (!account) {
        // Create new account for OAuth user
        account = await prisma.masteraccounts.create({
          data: {
            email,
            oauth_provider: provider,
            oauth_id: sub,
            role_type: 'user',
            email_verified: true, // OAuth emails are pre-verified
            created_at: new Date(),
            updated_at: new Date()
          }
        });

        // Create user profile
        const [firstName, ...lastNameParts] = name.split(' ');
        const lastName = lastNameParts.join(' ');

        const userProfile = await prisma.users.create({
          data: {
            account_id: account.account_id,
            first_name: firstName,
            last_name: lastName,
            created_at: new Date(),
            updated_at: new Date()
          }
        });

        // Update masteraccounts with role_id
        account = await prisma.masteraccounts.update({
          where: { account_id: account.account_id },
          data: {
            role_id: userProfile.user_id,
            updated_at: new Date()
          },
          include: { users: true }
        });

        account.users = [userProfile];
      } else {
        // Update existing account with OAuth info if not already set
        if (!account.oauth_provider) {
          await prisma.masteraccounts.update({
            where: { account_id: account.account_id },
            data: {
              oauth_provider: provider,
              oauth_id: sub,
              email_verified: true,
              updated_at: new Date()
            }
          });
        }
      }

      // Generate tokens
      const tokenPayload = {
        accountId: account.account_id,
        email: account.email,
        roleType: account.role_type
      };

      const tokens = this.generateTokens(tokenPayload);

      // Update last login
      await prisma.masteraccounts.update({
        where: { account_id: account.account_id },
        data: {
          last_login: new Date(),
          updated_at: new Date()
        }
      });

      return {
        account,
        userDetails: account.users[0] || null,
        ...tokens
      };

    } catch (error) {
      console.error('OAuth callback error:', error);
      throw error;
    }
  }

  // Sync OAuth user data from Supabase session
  async syncOAuthUser(supabaseUserData) {
    try {
      const { email, user_metadata, id: supabaseId, email_confirmed_at } = supabaseUserData;
      const fullName = user_metadata?.full_name || email;

      // Check if account exists by email
      let account = await prisma.masteraccounts.findUnique({
        where: { email },
        include: { users: true }
      });

      if (!account) {
        // Create new account
        account = await prisma.masteraccounts.create({
          data: {
            email,
            role_type: 'user',
            oauth_provider: 'google', // Assuming Google for now, can be made dynamic
            oauth_id: supabaseId,
            email_verified: email_confirmed_at ? true : false,
            created_at: new Date(),
            updated_at: new Date()
          }
        });

        // Create user profile
        const [firstName, ...lastNameParts] = fullName.split(' ');
        const lastName = lastNameParts.join(' ') || '';

        const userProfile = await prisma.users.create({
          data: {
            account_id: account.account_id,
            first_name: firstName,
            last_name: lastName,
            created_at: new Date(),
            updated_at: new Date()
          }
        });

        // Update masteraccounts with role_id
        account = await prisma.masteraccounts.update({
          where: { account_id: account.account_id },
          data: {
            role_id: userProfile.user_id,
            updated_at: new Date()
          },
          include: { users: true }
        });

        account.users = [userProfile];
      } else {
        // Update existing account with OAuth info if not already set
        if (!account.oauth_provider || !account.oauth_id) {
          await prisma.masteraccounts.update({
            where: { account_id: account.account_id },
            data: {
              oauth_provider: 'google',
              oauth_id: supabaseId,
              email_verified: email_confirmed_at ? true : account.email_verified,
              updated_at: new Date()
            }
          });
        }

        // Create user profile if it doesn't exist
        if (!account.users || account.users.length === 0) {
          const [firstName, ...lastNameParts] = fullName.split(' ');
          const lastName = lastNameParts.join(' ') || '';

          const userProfile = await prisma.users.create({
            data: {
              account_id: account.account_id,
              first_name: firstName,
              last_name: lastName,
              created_at: new Date(),
              updated_at: new Date()
            }
          });

          // Update masteraccounts with role_id if it's not set
          if (!account.role_id) {
            await prisma.masteraccounts.update({
              where: { account_id: account.account_id },
              data: {
                role_id: userProfile.user_id,
                updated_at: new Date()
              }
            });
          }

          account.users = [userProfile];
        }
      }

      // Generate tokens
      const tokenPayload = {
        accountId: account.account_id,
        email: account.email,
        roleType: account.role_type
      };

      const tokens = this.generateTokens(tokenPayload);

      // Update last login
      await prisma.masteraccounts.update({
        where: { account_id: account.account_id },
        data: {
          last_login: new Date(),
          updated_at: new Date()
        }
      });

      return {
        account,
        userDetails: account.users[0] || null,
        ...tokens
      };

    } catch (error) {
      console.error('OAuth user sync error:', error);
      throw new Error('Failed to sync OAuth user data');
    }
  }

  // Refresh token
  async refreshToken(refreshToken) {
    try {
      const decoded = this.verifyToken(refreshToken, true);

      // Find account
      const account = await prisma.masteraccounts.findUnique({
        where: { account_id: decoded.accountId },
        include: { users: true }
      });

      if (!account) {
        throw new Error('Invalid refresh token');
      }

      // Generate new tokens
      const tokenPayload = {
        accountId: account.account_id,
        email: account.email,
        roleType: account.role_type
      };

      const tokens = this.generateTokens(tokenPayload);

      return {
        account,
        userDetails: account.users[0] || null,
        ...tokens
      };

    } catch (error) {
      console.error('Token refresh error:', error);
      throw error;
    }
  }

  // Get user by account ID
  async getUserByAccountId(accountId) {
    try {
      const account = await prisma.masteraccounts.findUnique({
        where: { account_id: accountId },
        include: { users: true }
      });

      return account;
    } catch (error) {
      console.error('Get user error:', error);
      throw error;
    }
  }

  // Generate email verification token
  generateEmailVerificationToken() {
    return uuidv4();
  }

  // Generate reset token
  generateResetToken() {
    return uuidv4();
  }

  // Logout (placeholder for future token blacklisting)
  async logout(accountId) {
    // In a real implementation, you might want to blacklist the token
    // For now, we'll just return success
    return { success: true };
  }
}

module.exports = AuthService;
