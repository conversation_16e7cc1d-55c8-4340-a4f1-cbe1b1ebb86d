"use client"

import { useState } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { Home, Calendar, User, Menu, Heart } from "lucide-react"
import { useAuth } from "@/context/auth-context"
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet"

export default function MobileNav() {
  const pathname = usePathname()
  const { user } = useAuth()
  const [isOpen, setIsOpen] = useState(false)

  return (
    <>
      <div className="fixed bottom-0 left-0 right-0 z-50 md:hidden">
        <div className="bg-zinc-900 border-t border-zinc-800">
          <div className="flex items-center justify-around h-16">
            <Link
              href="/"
              className={`flex flex-col items-center justify-center w-full h-full ${
                pathname === "/" ? "text-red-500" : "text-zinc-400"
              }`}
            >
              <Home className="h-5 w-5" />
              <span className="text-xs mt-1">Home</span>
            </Link>

            <Link
              href="/events"
              className={`flex flex-col items-center justify-center w-full h-full ${
                pathname === "/events" || pathname.startsWith("/events/") ? "text-red-500" : "text-zinc-400"
              }`}
            >
              <Calendar className="h-5 w-5" />
              <span className="text-xs mt-1">Events</span>
            </Link>

            <Link
              href={user ? "/interested" : "/"}
              className={`flex flex-col items-center justify-center w-full h-full ${
                pathname === "/interested" ? "text-red-500" : "text-zinc-400"
              }`}
            >
              <Heart className="h-5 w-5" />
              <span className="text-xs mt-1">Interested</span>
            </Link>

            <Link
              href={user ? "/dashboard" : "/"}
              className={`flex flex-col items-center justify-center w-full h-full ${
                pathname === "/dashboard" ? "text-red-500" : "text-zinc-400"
              }`}
            >
              <User className="h-5 w-5" />
              <span className="text-xs mt-1">{user ? "Profile" : "Sign In"}</span>
            </Link>

            <Sheet open={isOpen} onOpenChange={setIsOpen}>
              <SheetTrigger asChild>
                <button className="flex flex-col items-center justify-center w-full h-full text-zinc-400">
                  <Menu className="h-5 w-5" />
                  <span className="text-xs mt-1">Menu</span>
                </button>
              </SheetTrigger>
              <SheetContent side="right" className="bg-zinc-900 text-white border-zinc-800">
                <div className="flex flex-col h-full py-6">
                  <h2 className="text-xl font-bold mb-6">Menu</h2>

                  <nav className="space-y-4">
                    <Link
                      href="/"
                      className={`block px-2 py-2 rounded-lg ${
                        pathname === "/" ? "bg-zinc-800 text-red-500" : "text-zinc-300 hover:bg-zinc-800"
                      }`}
                      onClick={() => setIsOpen(false)}
                    >
                      Home
                    </Link>
                    <Link
                      href="/events"
                      className={`block px-2 py-2 rounded-lg ${
                        pathname === "/events" ? "bg-zinc-800 text-red-500" : "text-zinc-300 hover:bg-zinc-800"
                      }`}
                      onClick={() => setIsOpen(false)}
                    >
                      Events
                    </Link>
                    <Link
                      href="/about"
                      className={`block px-2 py-2 rounded-lg ${
                        pathname === "/about" ? "bg-zinc-800 text-red-500" : "text-zinc-300 hover:bg-zinc-800"
                      }`}
                      onClick={() => setIsOpen(false)}
                    >
                      About
                    </Link>
                    <Link
                      href="/contact"
                      className={`block px-2 py-2 rounded-lg ${
                        pathname === "/contact" ? "bg-zinc-800 text-red-500" : "text-zinc-300 hover:bg-zinc-800"
                      }`}
                      onClick={() => setIsOpen(false)}
                    >
                      Contact
                    </Link>

                    {user ? (
                      <>
                        <div className="border-t border-zinc-800 my-4 pt-4"></div>
                        <Link
                          href="/dashboard"
                          className={`block px-2 py-2 rounded-lg ${
                            pathname === "/dashboard" ? "bg-zinc-800 text-red-500" : "text-zinc-300 hover:bg-zinc-800"
                          }`}
                          onClick={() => setIsOpen(false)}
                        >
                          Dashboard
                        </Link>
                        <Link
                          href="/wishlist"
                          className={`block px-2 py-2 rounded-lg ${
                            pathname === "/wishlist" ? "bg-zinc-800 text-red-500" : "text-zinc-300 hover:bg-zinc-800"
                          }`}
                          onClick={() => setIsOpen(false)}
                        >
                          Wishlist
                        </Link>
                      </>
                    ) : (
                      <>
                        <div className="border-t border-zinc-800 my-4 pt-4"></div>
                        <Link
                          href="/login"
                          className="block px-2 py-2 rounded-lg text-zinc-300 hover:bg-zinc-800"
                          onClick={() => setIsOpen(false)}
                        >
                          Sign In
                        </Link>
                        <Link
                          href="/register"
                          className="block px-2 py-2 rounded-lg text-white bg-red-600 hover:bg-red-700"
                          onClick={() => setIsOpen(false)}
                        >
                          Sign Up
                        </Link>
                      </>
                    )}
                  </nav>
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </div>
    </>
  )
}
