const express = require('express');
const InterestedController = require('../controllers/interestedController');
const { verifyTokenFromCookie } = require('../middleware/jwtCookieMiddleware');

const router = express.Router();
const interestedController = new InterestedController();

// All interested routes require authentication
router.use(verifyTokenFromCookie);

// Get user's interested events
// GET /api/interested
router.get('/', interestedController.getUserInterestedEvents);

// Add event to interested list
// POST /api/interested
router.post('/', interestedController.addToInterested);

// Check if event is in user's interested list
// GET /api/interested/check/:eventId
router.get('/check/:eventId', interestedController.checkInterestedStatus);

// Remove event from interested list
// DELETE /api/interested/:eventId
router.delete('/:eventId', interestedController.removeFromInterested);

module.exports = router;
