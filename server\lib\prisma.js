const { PrismaClient } = require('@prisma/client');

// Singleton pattern for Prisma client
class PrismaService {
  constructor() {
    if (PrismaService.instance) {
      return PrismaService.instance;
    }

    this.prisma = new PrismaClient({
      log: ['query', 'info', 'warn', 'error'],
    });

    PrismaService.instance = this;
  }

  getInstance() {
    return this.prisma;
  }

  async connect() {
    try {
      await this.prisma.$connect();
      console.log('Database connected successfully');
    } catch (error) {
      console.error('Database connection failed:', error);
      throw error;
    }
  }

  async disconnect() {
    try {
      await this.prisma.$disconnect();
      console.log('Database disconnected successfully');
    } catch (error) {
      console.error('Database disconnection failed:', error);
      throw error;
    }
  }
}

// Export singleton instance
const prismaService = new PrismaService();
module.exports = prismaService;
