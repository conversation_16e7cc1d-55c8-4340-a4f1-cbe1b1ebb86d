const express = require("express");
const SSLController = require("../controllers/sslController");
const { verifyTokenFromCookie } = require("../middleware/jwtCookieMiddleware");

const router = express.Router();
const sslController = new SSLController();

// Protected routes (require authentication)
router.use("/initiate", verifyTokenFromCookie);
router.use("/validate", verifyTokenFromCookie);

// Initiate payment
// POST /api/ssl/initiate
router.post("/initiate", sslController.initiatePayment);

// Validate payment
// POST /api/ssl/validate
router.post("/validate", sslController.validatePayment);

// Public routes (SSL Commerce callbacks - no authentication required)
// Payment success callback
// POST /api/ssl/success
router.post("/success", sslController.paymentSuccess);

// Payment failure callback
// POST /api/ssl/fail
router.post("/fail", sslController.paymentFailure);

// Payment cancellation callback
// POST /api/ssl/cancel
router.post("/cancel", sslController.paymentCancel);

// IPN (Instant Payment Notification) callback
// POST /api/ssl/ipn
router.post("/ipn", sslController.handleIPN);

module.exports = router;
