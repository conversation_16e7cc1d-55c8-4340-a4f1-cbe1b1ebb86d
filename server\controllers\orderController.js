const OrderService = require("../services/orderService");
const { PrismaClient } = require("@prisma/client");
const prisma = new PrismaClient();

class OrderController {
  constructor() {
    this.orderService = OrderService.getInstance();
  }

  // Helper method to get user_id from req.user
  async getUserId(user) {
    // If we have roleId and it's a user, use it directly
    if (user.roleId && user.roleType === "user") {
      return user.roleId;
    }

    // If we have profile data, use user_id from profile
    if (user.profile && user.profile.user_id) {
      return user.profile.user_id;
    }

    // If we have accountId, look up the user_id from the users table
    if (user.accountId) {
      const userRecord = await prisma.users.findUnique({
        where: { account_id: user.accountId },
        select: { user_id: true },
      });

      if (userRecord) {
        return userRecord.user_id;
      }
    }

    // If no user_id can be found, throw an error
    throw new Error(
      "User profile not found. Please complete your profile setup."
    );
  }

  // Get user's orders
  getUserOrders = async (req, res) => {
    try {
      const { roleType } = req.user;

      // Ensure user is a regular user (not admin or organizer)
      if (roleType !== "user") {
        return res.status(403).json({
          success: false,
          message: "Access denied. Only users can view orders.",
        });
      }

      const userId = await this.getUserId(req.user);
      const orders = await this.orderService.getUserOrders(userId);

      res.status(200).json({
        success: true,
        message: "Orders fetched successfully",
        data: {
          orders,
        },
      });
    } catch (error) {
      console.error("Error fetching user orders:", error);
      res.status(500).json({
        success: false,
        message: error.message || "Failed to fetch orders",
        data: null,
      });
    }
  };

  // Get user's tickets (formatted for dashboard)
  getUserTickets = async (req, res) => {
    try {
      const { roleType } = req.user;
      console.log("Fetching user tickets for user:", req.user);

      // Ensure user is a regular user (not admin or organizer)
      if (roleType !== "user") {
        return res.status(403).json({
          success: false,
          message: "Access denied. Only users can view tickets.",
        });
      }

      const userId = await this.getUserId(req.user);
      console.log("Resolved user ID:", userId);
      const tickets = await this.orderService.getUserTickets(userId);

      res.status(200).json({
        success: true,
        message: "Tickets fetched successfully",
        data: {
          tickets,
        },
      });
    } catch (error) {
      console.error("Error fetching user tickets:", error);
      res.status(500).json({
        success: false,
        message: error.message || "Failed to fetch tickets",
        data: null,
      });
    }
  };

  // Get specific order details
  getOrderById = async (req, res) => {
    try {
      const { roleType } = req.user;
      const { orderId } = req.params;

      // Ensure user is a regular user (not admin or organizer)
      if (roleType !== "user") {
        return res.status(403).json({
          success: false,
          message: "Access denied. Only users can view order details.",
        });
      }

      // Validate orderId
      const orderIdNum = parseInt(orderId);
      if (isNaN(orderIdNum)) {
        return res.status(400).json({
          success: false,
          message: "Invalid order ID",
        });
      }

      const userId = await this.getUserId(req.user);
      const order = await this.orderService.getOrderById(orderIdNum, userId);

      res.status(200).json({
        success: true,
        message: "Order details fetched successfully",
        data: {
          order,
        },
      });
    } catch (error) {
      console.error("Error fetching order details:", error);

      if (error.message === "Order not found") {
        return res.status(404).json({
          success: false,
          message: "Order not found",
        });
      }

      res.status(500).json({
        success: false,
        message: error.message || "Failed to fetch order details",
        data: null,
      });
    }
  };

  // Get user order statistics
  getUserOrderStats = async (req, res) => {
    try {
      const { roleType } = req.user;

      // Ensure user is a regular user (not admin or organizer)
      if (roleType !== "user") {
        return res.status(403).json({
          success: false,
          message: "Access denied. Only users can view order statistics.",
        });
      }

      const userId = await this.getUserId(req.user);
      const stats = await this.orderService.getUserOrderStats(userId);

      res.status(200).json({
        success: true,
        message: "Order statistics fetched successfully",
        data: {
          stats,
        },
      });
    } catch (error) {
      console.error("Error fetching order statistics:", error);
      res.status(500).json({
        success: false,
        message: error.message || "Failed to fetch order statistics",
        data: null,
      });
    }
  };

  // Create order from cart items with pending payment status
  createOrderFromCart = async (req, res) => {
    try {
      const { roleType } = req.user;

      // Ensure user is a regular user (not admin or organizer)
      if (roleType !== "user") {
        return res.status(403).json({
          success: false,
          message: "Access denied. Only users can create orders.",
        });
      }

      const userId = await this.getUserId(req.user);

      // Get user's cart items
      const cartItems = await prisma.cart.findMany({
        where: { user_id: userId },
        include: {
          tickettypes: {
            include: {
              events: true,
            },
          },
        },
      });

      if (cartItems.length === 0) {
        return res.status(400).json({
          success: false,
          message: "Cart is empty. Cannot create order.",
        });
      }

      // Create order from cart
      const result = await this.orderService.createOrderFromCart(
        userId,
        cartItems
      );

      res.status(201).json({
        success: true,
        message: "Order created successfully",
        data: result,
      });
    } catch (error) {
      console.error("Error creating order from cart:", error);
      res.status(500).json({
        success: false,
        message: error.message || "Failed to create order",
        data: null,
      });
    }
  };

  // Create order from selected tickets with attendee info (for direct purchase)
  createOrderFromTickets = async (req, res) => {
    try {
      const { roleType } = req.user;
      const { selectedTickets, ticketsWithAttendeeInfo } = req.body;

      // Ensure user is a regular user (not admin or organizer)
      if (roleType !== "user") {
        return res.status(403).json({
          success: false,
          message: "Access denied. Only users can create orders.",
        });
      }

      if (
        !selectedTickets ||
        !Array.isArray(selectedTickets) ||
        selectedTickets.length === 0
      ) {
        return res.status(400).json({
          success: false,
          message: "Selected tickets are required.",
        });
      }

      if (!ticketsWithAttendeeInfo || !Array.isArray(ticketsWithAttendeeInfo)) {
        return res.status(400).json({
          success: false,
          message: "Attendee information is required.",
        });
      }

      const userId = await this.getUserId(req.user);

      // Create order from tickets
      const result = await this.orderService.createOrderFromTickets(
        userId,
        selectedTickets,
        ticketsWithAttendeeInfo
      );

      res.status(201).json({
        success: true,
        message: "Order created successfully",
        data: result,
      });
    } catch (error) {
      console.error("Error creating order from tickets:", error);
      res.status(500).json({
        success: false,
        message: error.message || "Failed to create order",
        data: null,
      });
    }
  };

  // Update order payment status
  updateOrderPaymentStatus = async (req, res) => {
    try {
      const { roleType } = req.user;
      const { orderId } = req.params;
      const { paymentStatus, transactionId, paymentMethod } = req.body;

      // Ensure user is a regular user (not admin or organizer)
      if (roleType !== "user") {
        return res.status(403).json({
          success: false,
          message: "Access denied. Only users can update order payment status.",
        });
      }

      // Validate orderId
      const orderIdNum = parseInt(orderId);
      if (isNaN(orderIdNum)) {
        return res.status(400).json({
          success: false,
          message: "Invalid order ID",
        });
      }

      if (!paymentStatus || !transactionId) {
        return res.status(400).json({
          success: false,
          message: "Payment status and transaction ID are required.",
        });
      }

      const userId = await this.getUserId(req.user);

      // Verify order belongs to user
      const order = await prisma.orders.findFirst({
        where: {
          order_id: orderIdNum,
          user_id: userId,
        },
      });

      if (!order) {
        return res.status(404).json({
          success: false,
          message: "Order not found",
        });
      }

      // Update payment status
      const updatedOrder = await this.orderService.updateOrderPaymentStatus(
        orderIdNum,
        paymentStatus,
        transactionId,
        paymentMethod
      );

      res.status(200).json({
        success: true,
        message: "Order payment status updated successfully",
        data: { order: updatedOrder },
      });
    } catch (error) {
      console.error("Error updating order payment status:", error);
      res.status(500).json({
        success: false,
        message: error.message || "Failed to update order payment status",
        data: null,
      });
    }
  };

  // Get user's pending orders
  getUserPendingOrders = async (req, res) => {
    try {
      const { roleType } = req.user;

      // Ensure user is a regular user (not admin or organizer)
      if (roleType !== "user") {
        return res.status(403).json({
          success: false,
          message: "Access denied. Only users can view pending orders.",
        });
      }

      const userId = await this.getUserId(req.user);
      const pendingOrders = await this.orderService.getUserPendingOrders(
        userId
      );

      res.status(200).json({
        success: true,
        message: "Pending orders fetched successfully",
        data: { orders: pendingOrders },
      });
    } catch (error) {
      console.error("Error fetching pending orders:", error);
      res.status(500).json({
        success: false,
        message: error.message || "Failed to fetch pending orders",
        data: null,
      });
    }
  };

  // Get user's pending orders filtered by eventId
  getUserPendingOrdersByEvent = async (req, res) => {
    try {
      const { roleType } = req.user;
      const { eventId } = req.params;

      // Ensure user is a regular user (not admin or organizer)
      if (roleType !== "user") {
        return res.status(403).json({
          success: false,
          message: "Access denied. Only users can view pending orders.",
        });
      }

      if (!eventId || isNaN(parseInt(eventId))) {
        return res.status(400).json({
          success: false,
          message: "Valid event ID is required",
        });
      }

      const userId = await this.getUserId(req.user);
      const pendingOrders = await this.orderService.getUserPendingOrdersByEvent(
        userId,
        parseInt(eventId)
      );

      res.status(200).json({
        success: true,
        message: "Event-specific pending orders fetched successfully",
        data: { orders: pendingOrders },
      });
    } catch (error) {
      console.error("Error fetching event-specific pending orders:", error);
      res.status(500).json({
        success: false,
        message: error.message || "Failed to fetch pending orders",
        data: null,
      });
    }
  };

  // ===== CART-LIKE FUNCTIONALITY USING PENDING ORDERS =====

  // Get cart items (pending order items)
  getCartItems = async (req, res) => {
    try {
      const { roleType } = req.user;

      // Ensure user is a regular user (not admin or organizer)
      if (roleType !== "user") {
        return res.status(403).json({
          success: false,
          message: "Access denied. Only users can view cart items.",
        });
      }

      const userId = await this.getUserId(req.user);
      const cartItems = await this.orderService.getCartItems(userId);

      res.status(200).json({
        success: true,
        message: "Cart items fetched successfully",
        data: cartItems,
      });
    } catch (error) {
      console.error("Error fetching cart items:", error);
      res.status(500).json({
        success: false,
        message: error.message || "Failed to fetch cart items",
        data: null,
      });
    }
  };

  // Add item to cart (create pending order item)
  addToCart = async (req, res) => {
    try {
      const { roleType } = req.user;
      const { ticketTypeId, quantity = 1 } = req.body;

      // Ensure user is a regular user (not admin or organizer)
      if (roleType !== "user") {
        return res.status(403).json({
          success: false,
          message: "Access denied. Only users can add items to cart.",
        });
      }

      if (!ticketTypeId) {
        return res.status(400).json({
          success: false,
          message: "Ticket type ID is required",
        });
      }

      if (quantity <= 0 || !Number.isInteger(quantity)) {
        return res.status(400).json({
          success: false,
          message: "Quantity must be a positive integer",
        });
      }

      const userId = await this.getUserId(req.user);
      const cartItem = await this.orderService.addToCart(
        userId,
        ticketTypeId,
        quantity
      );

      res.status(201).json({
        success: true,
        message: "Item added to cart successfully",
        data: cartItem,
      });
    } catch (error) {
      console.error("Error adding to cart:", error);

      // Handle specific error cases
      if (
        error.message.includes("not found") ||
        error.message.includes("Not enough tickets") ||
        error.message.includes("Maximum")
      ) {
        return res.status(400).json({
          success: false,
          message: error.message,
        });
      }

      res.status(500).json({
        success: false,
        message: error.message || "Failed to add item to cart",
        data: null,
      });
    }
  };

  // Update cart item quantity (update pending order item)
  updateCartItemQuantity = async (req, res) => {
    try {
      const { roleType } = req.user;
      const { cartId } = req.params;
      const { quantity } = req.body;

      // Ensure user is a regular user (not admin or organizer)
      if (roleType !== "user") {
        return res.status(403).json({
          success: false,
          message: "Access denied. Only users can update cart items.",
        });
      }

      if (!cartId || isNaN(parseInt(cartId))) {
        return res.status(400).json({
          success: false,
          message: "Valid cart ID is required",
        });
      }

      if (!quantity || quantity <= 0 || !Number.isInteger(quantity)) {
        return res.status(400).json({
          success: false,
          message: "Quantity must be a positive integer",
        });
      }

      const userId = await this.getUserId(req.user);
      const updatedCartItem = await this.orderService.updateCartItemQuantity(
        userId,
        parseInt(cartId),
        quantity
      );

      res.status(200).json({
        success: true,
        message: "Cart item updated successfully",
        data: updatedCartItem,
      });
    } catch (error) {
      console.error("Error updating cart item:", error);

      // Handle specific error cases
      if (
        error.message.includes("not found") ||
        error.message.includes("Not enough tickets") ||
        error.message.includes("Maximum")
      ) {
        return res.status(400).json({
          success: false,
          message: error.message,
        });
      }

      res.status(500).json({
        success: false,
        message: error.message || "Failed to update cart item",
        data: null,
      });
    }
  };

  // Remove item from cart (remove pending order item)
  removeFromCart = async (req, res) => {
    try {
      const { roleType } = req.user;
      const { cartId } = req.params;

      // Ensure user is a regular user (not admin or organizer)
      if (roleType !== "user") {
        return res.status(403).json({
          success: false,
          message: "Access denied. Only users can remove cart items.",
        });
      }

      if (!cartId || isNaN(parseInt(cartId))) {
        return res.status(400).json({
          success: false,
          message: "Valid cart ID is required",
        });
      }

      const userId = await this.getUserId(req.user);
      await this.orderService.removeFromCart(userId, parseInt(cartId));

      res.status(200).json({
        success: true,
        message: "Item removed from cart successfully",
      });
    } catch (error) {
      console.error("Error removing from cart:", error);

      if (error.message.includes("not found")) {
        return res.status(400).json({
          success: false,
          message: error.message,
        });
      }

      res.status(500).json({
        success: false,
        message: error.message || "Failed to remove item from cart",
        data: null,
      });
    }
  };

  // Clear entire cart (delete all pending orders)
  clearCart = async (req, res) => {
    try {
      const { roleType } = req.user;

      // Ensure user is a regular user (not admin or organizer)
      if (roleType !== "user") {
        return res.status(403).json({
          success: false,
          message: "Access denied. Only users can clear cart.",
        });
      }

      const userId = await this.getUserId(req.user);
      await this.orderService.clearCart(userId);

      res.status(200).json({
        success: true,
        message: "Cart cleared successfully",
      });
    } catch (error) {
      console.error("Error clearing cart:", error);
      res.status(500).json({
        success: false,
        message: error.message || "Failed to clear cart",
        data: null,
      });
    }
  };

  // Get cart summary (total items, total price)
  getCartSummary = async (req, res) => {
    try {
      const { roleType } = req.user;

      // Ensure user is a regular user (not admin or organizer)
      if (roleType !== "user") {
        return res.status(403).json({
          success: false,
          message: "Access denied. Only users can view cart summary.",
        });
      }

      const userId = await this.getUserId(req.user);
      const summary = await this.orderService.getCartSummary(userId);

      res.status(200).json({
        success: true,
        message: "Cart summary fetched successfully",
        data: summary,
      });
    } catch (error) {
      console.error("Error getting cart summary:", error);
      res.status(500).json({
        success: false,
        message: error.message || "Failed to get cart summary",
        data: null,
      });
    }
  };
}

module.exports = OrderController;
