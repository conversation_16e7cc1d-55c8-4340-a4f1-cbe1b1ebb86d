const express = require('express');
const EventController = require('../controllers/eventController');

const router = express.Router();
const eventController = new EventController();

// Public routes - no authentication required

// Get all events (no server-side filtering)
// GET /api/events
router.get('/', eventController.getAllEvents);

// Get all genres
// GET /api/events/genres
router.get('/genres', eventController.getAllGenres);

// Get all locations
// GET /api/events/locations
router.get('/locations', eventController.getAllLocations);

// Get events by organizer
// GET /api/events/organizer/1
router.get('/organizer/:organizerId', eventController.getEventsByOrganizer);

// Get single event by ID
// GET /api/events/123
router.get('/:id', eventController.getEventById);

module.exports = router;
