generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

model admins {
  admin_id       Int             @id @default(autoincrement())
  account_id     Int?            @unique
  name           String?         @db.VarChar(100)
  created_at     DateTime?       @default(now()) @db.Timestamp(6)
  masteraccounts masteraccounts? @relation(fields: [account_id], references: [account_id], onDelete: Cascade)
}

model artists {
  artist_id    Int            @id @default(autoincrement())
  name         String         @db.VarChar(255)
  bio          String?
  image        String?        @db.VarChar(255)
  eventartists eventartists[]
}

model cart {
  cart_id        Int         @id @default(autoincrement())
  user_id        Int
  ticket_type_id Int
  quantity       Int         @default(1)
  created_at     DateTime?   @default(now()) @db.Timestamp(6)
  updated_at     DateTime?   @default(now()) @db.Timestamp(6)
  tickettypes    tickettypes @relation(fields: [ticket_type_id], references: [ticket_type_id], onDelete: NoAction, onUpdate: NoAction)
  users          users       @relation(fields: [user_id], references: [user_id], onDelete: NoAction, onUpdate: NoAction)
}

model categorysales {
  category_sales_id Int              @id @default(autoincrement())
  event_id          Int?
  organizer_id      Int?
  category_id       Int?
  tickets_sold      Int?
  revenue           Decimal?         @db.Decimal
  last_updated      DateTime?        @db.Timestamp(6)
  eventcategories   eventcategories? @relation(fields: [category_id], references: [category_id], onDelete: NoAction, onUpdate: NoAction)
  events            events?          @relation(fields: [event_id], references: [event_id], onDelete: NoAction, onUpdate: NoAction)
  organizers        organizers?      @relation(fields: [organizer_id], references: [organizer_id], onDelete: NoAction, onUpdate: NoAction)
}

model dailysales {
  daily_sales_id Int         @id @default(autoincrement())
  event_id       Int?
  organizer_id   Int?
  sale_date      DateTime?   @db.Date
  tickets_sold   Int?
  revenue        Decimal?    @db.Decimal
  events         events?     @relation(fields: [event_id], references: [event_id], onDelete: NoAction, onUpdate: NoAction)
  organizers     organizers? @relation(fields: [organizer_id], references: [organizer_id], onDelete: NoAction, onUpdate: NoAction)

  @@unique([event_id, sale_date])
}

model emailreminders {
  reminder_id    Int       @id @default(autoincrement())
  user_id        Int
  event_id       Int
  email_sent     Boolean?  @default(false)
  scheduled_date DateTime  @db.Date
  mail_text      String?   @db.VarChar(255)
  created_at     DateTime? @default(now()) @db.Timestamp(6)
  events         events    @relation(fields: [event_id], references: [event_id], onDelete: NoAction, onUpdate: NoAction)
  users          users     @relation(fields: [user_id], references: [user_id], onDelete: NoAction, onUpdate: NoAction)
}

model eventartists {
  event_id  Int
  artist_id Int
  artists   artists @relation(fields: [artist_id], references: [artist_id], onDelete: NoAction, onUpdate: NoAction)
  events    events  @relation(fields: [event_id], references: [event_id], onDelete: NoAction, onUpdate: NoAction)

  @@id([event_id, artist_id])
}

model eventcategories {
  category_id   Int             @id @default(autoincrement())
  event_id      Int
  name          String          @db.VarChar(100)
  description   String?
  category_type String?         @db.VarChar(100)
  categorysales categorysales[]
  events        events          @relation(fields: [event_id], references: [event_id], onDelete: NoAction, onUpdate: NoAction)
  tickettypes   tickettypes[]
}

model events {
  event_id           Int                @id @default(autoincrement())
  organizer_id       Int
  title              String             @db.VarChar(255)
  description        String?
  start_date         DateTime           @db.Date
  end_date           DateTime           @db.Date
  start_time         DateTime           @db.Time(6)
  banner_image       String?            @db.VarChar(255)
  venue_name         String?            @db.VarChar(255)
  location_id        Int?
  genre_id           Int?
  tickets_sale_start DateTime           @db.Timestamp(6)
  tickets_sale_end   DateTime           @db.Timestamp(6)
  event_policy       String?
  status             event_status_type? @default(draft)
  created_at         DateTime?          @default(now()) @db.Timestamp(6)
  updated_at         DateTime?          @default(now()) @db.Timestamp(6)
  categorysales      categorysales[]
  dailysales         dailysales[]
  emailreminders     emailreminders[]
  eventartists       eventartists[]
  eventcategories    eventcategories[]
  genres             genres?            @relation(fields: [genre_id], references: [genre_id], onDelete: NoAction, onUpdate: NoAction)
  locations          locations?         @relation(fields: [location_id], references: [location_id], onDelete: NoAction, onUpdate: NoAction)
  organizers         organizers         @relation(fields: [organizer_id], references: [organizer_id], onDelete: NoAction, onUpdate: NoAction)
  eventsales         eventsales[]
  notifications      notifications[]
  tickettypes        tickettypes[]
  tickettypesales    tickettypesales[]
  wishlists          wishlists[]
}

model eventsales {
  event_sales_id Int         @id @default(autoincrement())
  event_id       Int?
  organizer_id   Int?
  tickets_sold   Int?
  total_revenue  Decimal?    @db.Decimal
  last_updated   DateTime?   @db.Timestamp(6)
  events         events?     @relation(fields: [event_id], references: [event_id], onDelete: NoAction, onUpdate: NoAction)
  organizers     organizers? @relation(fields: [organizer_id], references: [organizer_id], onDelete: NoAction, onUpdate: NoAction)
}

model genres {
  genre_id Int      @id @default(autoincrement())
  name     String   @unique @db.VarChar(100)
  icon     String?  @db.VarChar(255)
  events   events[]
}

model locations {
  location_id Int      @id @default(autoincrement())
  city        String   @db.VarChar(100)
  venue_name  String?  @db.VarChar(255)
  address     String?
  map_link    String?  @db.VarChar(255)
  events      events[]
}

/// This table contains check constraints and requires additional setup for migrations. Visit https://pris.ly/d/check-constraints for more info.
model masteraccounts {
  account_id          Int       @id @default(autoincrement())
  email               String    @unique @db.VarChar(255)
  password_hash       String?   @db.VarChar(255)
  role_type           String    @db.VarChar(50)
  role_id             Int?
  oauth_provider      String?   @db.VarChar(50)
  oauth_id            String?   @db.VarChar(255)
  email_verified      Boolean?  @default(false)
  verification_token  String?   @db.VarChar(255)
  reset_token         String?   @db.VarChar(255)
  reset_token_expires DateTime? @db.Timestamp(6)
  last_login          DateTime? @db.Timestamp(6)
  created_at          DateTime? @default(now()) @db.Timestamp(6)
  updated_at          DateTime? @default(now()) @db.Timestamp(6)
  users               users[]
  organizers          organizers[]
  admins              admins[]
}

model notifications {
  notification_id Int               @id @default(autoincrement())
  sender_id       Int?
  sender_type     sender_type_enum? @default(system)
  recipient_id    Int
  event_id        Int?
  title           String            @db.VarChar(255)
  message         String
  is_read         Boolean?          @default(false)
  created_at      DateTime?         @default(now()) @db.Timestamp(6)
  events          events?           @relation(fields: [event_id], references: [event_id], onDelete: NoAction, onUpdate: NoAction)
  users           users             @relation(fields: [recipient_id], references: [user_id], onDelete: NoAction, onUpdate: NoAction)
  organizers      organizers?       @relation(fields: [sender_id], references: [organizer_id], onDelete: NoAction, onUpdate: NoAction)
}

model orderitems {
  order_item_id  Int         @id @default(autoincrement())
  order_id       Int
  ticket_type_id Int
  quantity       Int         @default(1)
  unit_price     Decimal     @db.Decimal(10, 2)
  orders         orders      @relation(fields: [order_id], references: [order_id], onDelete: NoAction, onUpdate: NoAction)
  tickettypes    tickettypes @relation(fields: [ticket_type_id], references: [ticket_type_id], onDelete: NoAction, onUpdate: NoAction)
}

model orders {
  order_id        Int                  @id @default(autoincrement())
  user_id         Int
  total_amount    Decimal              @db.Decimal(10, 2)
  additional_fees Decimal?             @default(0.00) @db.Decimal(10, 2)
  payment_status  payment_status_type? @default(pending)
  payment_method  String?              @db.VarChar(50)
  transaction_id  String?              @db.VarChar(255)
  created_at      DateTime?            @default(now()) @db.Timestamp(6)
  orderitems      orderitems[]
  users           users                @relation(fields: [user_id], references: [user_id], onDelete: NoAction, onUpdate: NoAction)
  tickets         tickets[]
}

model organizers {
  organizer_id      Int                    @id @default(autoincrement())
  account_id        Int?                   @unique
  organization_name String                 @db.VarChar(255)
  phone_number      String                 @db.VarChar(20)
  logo              String?                @db.VarChar(255)
  description       String?
  facebook_link     String?                @db.VarChar(255)
  insta_link        String?                @db.VarChar(255)
  web_link          String?                @db.VarChar(255)
  status            organizer_status_type? @default(pending)
  created_at        DateTime?              @default(now()) @db.Timestamp(6)
  updated_at        DateTime?              @default(now()) @db.Timestamp(6)
  masteraccounts    masteraccounts?        @relation(fields: [account_id], references: [account_id], onDelete: Cascade)
  categorysales     categorysales[]
  dailysales        dailysales[]
  events            events[]
  eventsales        eventsales[]
  notifications     notifications[]
  organizersales    organizersales[]
  tickettypesales   tickettypesales[]
}

model organizersales {
  organizer_sales_id Int         @id @default(autoincrement())
  organizer_id       Int?
  total_events       Int?
  active_events      Int?
  total_tickets_sold Int?
  total_revenue      Decimal?    @db.Decimal
  last_updated       DateTime?   @db.Timestamp(6)
  organizers         organizers? @relation(fields: [organizer_id], references: [organizer_id], onDelete: NoAction, onUpdate: NoAction)
}

model tickets {
  ticket_id       Int         @id @default(autoincrement())
  order_id        Int
  ticket_type_id  Int
  qr_code         String      @unique @db.VarChar(255)
  is_validated    Boolean?    @default(false)
  validation_time DateTime?   @db.Timestamp(6)
  created_at      DateTime?   @default(now()) @db.Timestamp(6)
  user_ticketpdf  String?     @db.VarChar(255)

  attendee_name     String?     @db.VarChar(255)
  attendee_email    String?     @db.VarChar(255)
  attendee_phone    String?     @db.VarChar(255)
  
  orders          orders      @relation(fields: [order_id], references: [order_id], onDelete: NoAction, onUpdate: NoAction)
  tickettypes     tickettypes @relation(fields: [ticket_type_id], references: [ticket_type_id], onDelete: NoAction, onUpdate: NoAction)
}

model tickettypes {
  ticket_type_id     Int               @id @default(autoincrement())
  event_id           Int
  category_id        Int?
  name               String            @db.VarChar(100)
  description        String?
  price              Decimal           @db.Decimal(10, 2)
  quantity_available Int
  max_per_order      Int?              @default(10)
  banner             String?           @db.VarChar(255)
  pdf_template       String?           @db.VarChar(255)
  cart               cart[]
  orderitems         orderitems[]
  tickets            tickets[]
  eventcategories    eventcategories?  @relation(fields: [category_id], references: [category_id], onDelete: NoAction, onUpdate: NoAction)
  events             events            @relation(fields: [event_id], references: [event_id], onDelete: NoAction, onUpdate: NoAction)
  tickettypesales    tickettypesales[]
}

model tickettypesales {
  ticket_type_sales_id Int          @id @default(autoincrement())
  event_id             Int?
  organizer_id         Int?
  ticket_type_id       Int?
  tickets_sold         Int?
  revenue              Decimal?     @db.Decimal
  last_updated         DateTime?    @db.Timestamp(6)
  events               events?      @relation(fields: [event_id], references: [event_id], onDelete: NoAction, onUpdate: NoAction)
  organizers           organizers?  @relation(fields: [organizer_id], references: [organizer_id], onDelete: NoAction, onUpdate: NoAction)
  tickettypes          tickettypes? @relation(fields: [ticket_type_id], references: [ticket_type_id], onDelete: NoAction, onUpdate: NoAction)
}

model users {
  user_id        Int              @id @default(autoincrement())
  account_id     Int?             @unique
  first_name     String           @db.VarChar(100)
  last_name      String           @db.VarChar(100)
  phone_number   String?          @db.VarChar(20)
  profile_image  String?          @db.VarChar(255)
  gender         String?          @db.VarChar(255)
  dob            DateTime?        @db.Timestamp(6)
  created_at     DateTime?        @default(now()) @db.Timestamp(6)
  updated_at     DateTime?        @default(now()) @db.Timestamp(6)
  masteraccounts masteraccounts?  @relation(fields: [account_id], references: [account_id], onDelete: Cascade)
  cart           cart[]
  emailreminders emailreminders[]
  notifications  notifications[]
  orders         orders[]
  wishlists      wishlists[]
}

model wishlists {
  wishlist_id Int       @id @default(autoincrement())
  user_id     Int
  event_id    Int
  created_at  DateTime? @default(now()) @db.Timestamp(6)
  events      events    @relation(fields: [event_id], references: [event_id], onDelete: NoAction, onUpdate: NoAction)
  users       users     @relation(fields: [user_id], references: [user_id], onDelete: NoAction, onUpdate: NoAction)

  @@unique([user_id, event_id])
}

enum event_status_type {
  draft
  pending
  approved
  live
  completed
  cancelled
}

enum oauth_provider_type {
  google
  facebook
  none
}

enum organizer_status_type {
  pending
  approved
  rejected
}

enum payment_status_type {
  pending
  completed
  failed
  refunded
}

enum sender_type_enum {
  system
  organizer
}
