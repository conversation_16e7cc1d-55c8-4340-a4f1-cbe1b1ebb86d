# Checkout System Test Plan

## Overview
Testing the updated checkout system that uses orders/orderItems tables instead of sessionStorage for pending orders.

## Test Scenarios

### 1. Cart-based Checkout (All Events)
- **Setup**: Add items to cart from multiple events
- **Action**: Navigate to `/checkout` (no eventId parameter)
- **Expected**: Should fetch all pending orders and display them grouped by event

### 2. Event-specific Checkout
- **Setup**: Add items to cart from multiple events
- **Action**: Navigate to `/checkout?eventId=1` 
- **Expected**: Should fetch only pending orders for event ID 1

### 3. Direct Purchase Flow
- **Setup**: Use ticket info modal to purchase tickets directly
- **Action**: Complete attendee info and proceed to checkout
- **Expected**: Should create pending order and redirect to checkout

### 4. Payment Processing
- **Setup**: Have pending orders in checkout
- **Action**: Click "Proceed to Pay"
- **Expected**: Should initiate SSL payment with existing order ID

## API Endpoints to Test

### Get All Pending Orders
```
GET /api/orders/pending
Authorization: Bearer <token>
```

### Get Event-specific Pending Orders
```
GET /api/orders/pending/:eventId
Authorization: Bearer <token>
```

## Key Changes Made

1. **Checkout Context**: 
   - Removed dependency on sessionStorage for pending orders
   - Added `initializeCheckout(eventId)` function
   - Uses `getUserPendingOrders()` and `getUserPendingOrdersByEvent(eventId)` APIs

2. **Checkout Page**:
   - Removed sessionStorage logic for pending orders
   - Uses checkout context for all data
   - Maintains sessionStorage only for `ticketAttendeeInfo`

3. **New API Endpoints**:
   - `GET /api/orders/pending/:eventId` - Get pending orders for specific event
   - Backend service method `getUserPendingOrdersByEvent(userId, eventId)`

## Testing Steps

1. **Start both client and server**
2. **Login as a user**
3. **Add items to cart from different events**
4. **Test cart-based checkout** (`/checkout`)
5. **Test event-specific checkout** (`/checkout?eventId=1`)
6. **Verify attendee info is still retrieved from sessionStorage**
7. **Test payment initiation**

## Expected Behavior

- Checkout page should load pending orders from database
- Event filtering should work correctly
- Payment processing should use existing order IDs
- Attendee information should still come from sessionStorage
- No more dependency on sessionStorage for order data
