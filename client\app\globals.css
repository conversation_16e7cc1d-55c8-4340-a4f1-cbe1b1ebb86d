@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 240 10% 3.9%; /* Dark background like event detail page */
    --foreground: 0 0% 98%; /* Light text on dark background */
    --card: 240 10% 5.9%; /* Slightly lighter than background for cards */
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 5.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 72% 51%; /* Red primary color */
    --primary-foreground: 0 0% 98%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 0 72% 51%;
    --radius: 0.5rem;
  }

  /* Force dark mode for all users */
  html {
    color-scheme: dark;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-zinc-950 text-white;
    transition: background-color 0.3s ease, color 0.3s ease;
  }
}

/* Custom Animations */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.slide-up {
  animation: slideUp 0.5s ease-in-out;
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Snap Scrolling */
.snap-container {
  scroll-snap-type: y mandatory;
  overflow-y: scroll;
  height: 100vh;
}

.snap-section {
  scroll-snap-align: start;
  height: 100vh;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background-color: #27272a; /* zinc-800 */
}

::-webkit-scrollbar-thumb {
  background-color: #dc2626; /* red-600 */
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: #b91c1c; /* red-700 */
}

/* Grid Background */
body {
  background-color: #09090b; /* zinc-950 */
  background-image: linear-gradient(rgba(255, 255, 255, 0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
  background-size: 20px 20px; /* Small grid size */
}

/* Card styling consistent with event detail page */
.app-card {
  @apply bg-zinc-800 rounded-lg border border-zinc-700 shadow-md overflow-hidden;
}

/* Button styling consistent with event detail page */
.app-button-primary {
  @apply bg-red-600 hover:bg-red-700 text-white font-medium rounded-md transition-colors;
}

/* Gradient overlay for images like in event detail page */
.image-gradient-overlay {
  @apply relative;
}

.image-gradient-overlay::after {
  content: "";
  @apply absolute inset-0 bg-gradient-to-t from-zinc-950 to-transparent;
}
