"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-collapsible_f5622cb202571cb8469c791d8ff9ca86";
exports.ids = ["vendor-chunks/@radix-ui+react-collapsible_f5622cb202571cb8469c791d8ff9ca86"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-collapsible_f5622cb202571cb8469c791d8ff9ca86/node_modules/@radix-ui/react-collapsible/dist/index.mjs":
/*!*************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-collapsible_f5622cb202571cb8469c791d8ff9ca86/node_modules/@radix-ui/react-collapsible/dist/index.mjs ***!
  \*************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Collapsible: () => (/* binding */ Collapsible),\n/* harmony export */   CollapsibleContent: () => (/* binding */ CollapsibleContent),\n/* harmony export */   CollapsibleTrigger: () => (/* binding */ CollapsibleTrigger),\n/* harmony export */   Content: () => (/* binding */ Content),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   Trigger: () => (/* binding */ Trigger),\n/* harmony export */   createCollapsibleScope: () => (/* binding */ createCollapsibleScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+primitive@1.1.2/node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-context@1.1_ad42a61e498c34b6ab0064ec44eba795/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-control_c699384c7778101ecedcd597aadb895d/node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-layout-_3aa1064605213fb84b843d985c232dd9/node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-compose-ref_9f08440bbab3ef806add91f73ce9eac4/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2_6e0f845fa0b5165e723599b67dc13bbf/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-presence */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-presence@1._949a0df3eae86665e086aa01aee25ebf/node_modules/@radix-ui/react-presence/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-id */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-id@1.1.1_@types+react@19.1.8_react@19.1.0/node_modules/@radix-ui/react-id/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Collapsible,CollapsibleContent,CollapsibleTrigger,Content,Root,Trigger,createCollapsibleScope auto */ // src/collapsible.tsx\n\n\n\n\n\n\n\n\n\n\nvar COLLAPSIBLE_NAME = \"Collapsible\";\nvar [createCollapsibleContext, createCollapsibleScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(COLLAPSIBLE_NAME);\nvar [CollapsibleProvider, useCollapsibleContext] = createCollapsibleContext(COLLAPSIBLE_NAME);\nvar Collapsible = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeCollapsible, open: openProp, defaultOpen, disabled, onOpenChange, ...collapsibleProps } = props;\n    const [open, setOpen] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_3__.useControllableState)({\n        prop: openProp,\n        defaultProp: defaultOpen ?? false,\n        onChange: onOpenChange,\n        caller: COLLAPSIBLE_NAME\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollapsibleProvider, {\n        scope: __scopeCollapsible,\n        disabled,\n        contentId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__.useId)(),\n        open,\n        onOpenToggle: react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n            \"Collapsible.useCallback\": ()=>setOpen({\n                    \"Collapsible.useCallback\": (prevOpen)=>!prevOpen\n                }[\"Collapsible.useCallback\"])\n        }[\"Collapsible.useCallback\"], [\n            setOpen\n        ]),\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.div, {\n            \"data-state\": getState(open),\n            \"data-disabled\": disabled ? \"\" : void 0,\n            ...collapsibleProps,\n            ref: forwardedRef\n        })\n    });\n});\nCollapsible.displayName = COLLAPSIBLE_NAME;\nvar TRIGGER_NAME = \"CollapsibleTrigger\";\nvar CollapsibleTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeCollapsible, ...triggerProps } = props;\n    const context = useCollapsibleContext(TRIGGER_NAME, __scopeCollapsible);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.button, {\n        type: \"button\",\n        \"aria-controls\": context.contentId,\n        \"aria-expanded\": context.open || false,\n        \"data-state\": getState(context.open),\n        \"data-disabled\": context.disabled ? \"\" : void 0,\n        disabled: context.disabled,\n        ...triggerProps,\n        ref: forwardedRef,\n        onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_6__.composeEventHandlers)(props.onClick, context.onOpenToggle)\n    });\n});\nCollapsibleTrigger.displayName = TRIGGER_NAME;\nvar CONTENT_NAME = \"CollapsibleContent\";\nvar CollapsibleContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { forceMount, ...contentProps } = props;\n    const context = useCollapsibleContext(CONTENT_NAME, props.__scopeCollapsible);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_7__.Presence, {\n        present: forceMount || context.open,\n        children: ({ present })=>/* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CollapsibleContentImpl, {\n                ...contentProps,\n                ref: forwardedRef,\n                present\n            })\n    });\n});\nCollapsibleContent.displayName = CONTENT_NAME;\nvar CollapsibleContentImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeCollapsible, present, children, ...contentProps } = props;\n    const context = useCollapsibleContext(CONTENT_NAME, __scopeCollapsible);\n    const [isPresent, setIsPresent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(present);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_8__.useComposedRefs)(forwardedRef, ref);\n    const heightRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const height = heightRef.current;\n    const widthRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const width = widthRef.current;\n    const isOpen = context.open || isPresent;\n    const isMountAnimationPreventedRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(isOpen);\n    const originalStylesRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(void 0);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"CollapsibleContentImpl.useEffect\": ()=>{\n            const rAF = requestAnimationFrame({\n                \"CollapsibleContentImpl.useEffect.rAF\": ()=>isMountAnimationPreventedRef.current = false\n            }[\"CollapsibleContentImpl.useEffect.rAF\"]);\n            return ({\n                \"CollapsibleContentImpl.useEffect\": ()=>cancelAnimationFrame(rAF)\n            })[\"CollapsibleContentImpl.useEffect\"];\n        }\n    }[\"CollapsibleContentImpl.useEffect\"], []);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_9__.useLayoutEffect)({\n        \"CollapsibleContentImpl.useLayoutEffect\": ()=>{\n            const node = ref.current;\n            if (node) {\n                originalStylesRef.current = originalStylesRef.current || {\n                    transitionDuration: node.style.transitionDuration,\n                    animationName: node.style.animationName\n                };\n                node.style.transitionDuration = \"0s\";\n                node.style.animationName = \"none\";\n                const rect = node.getBoundingClientRect();\n                heightRef.current = rect.height;\n                widthRef.current = rect.width;\n                if (!isMountAnimationPreventedRef.current) {\n                    node.style.transitionDuration = originalStylesRef.current.transitionDuration;\n                    node.style.animationName = originalStylesRef.current.animationName;\n                }\n                setIsPresent(present);\n            }\n        }\n    }[\"CollapsibleContentImpl.useLayoutEffect\"], [\n        context.open,\n        present\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.div, {\n        \"data-state\": getState(context.open),\n        \"data-disabled\": context.disabled ? \"\" : void 0,\n        id: context.contentId,\n        hidden: !isOpen,\n        ...contentProps,\n        ref: composedRefs,\n        style: {\n            [`--radix-collapsible-content-height`]: height ? `${height}px` : void 0,\n            [`--radix-collapsible-content-width`]: width ? `${width}px` : void 0,\n            ...props.style\n        },\n        children: isOpen && children\n    });\n});\nfunction getState(open) {\n    return open ? \"open\" : \"closed\";\n}\nvar Root = Collapsible;\nvar Trigger = CollapsibleTrigger;\nvar Content = CollapsibleContent;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-collapsible_f5622cb202571cb8469c791d8ff9ca86/node_modules/@radix-ui/react-collapsible/dist/index.mjs\n");

/***/ })

};
;