"use client"

import { useAdmin } from "@/context/admin-context"
import AdminLayout from "@/components/admin/admin-layout"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Users,
  Calendar,
  DollarSign,
  TrendingUp,
  Building2,
  CheckCircle,
  Clock,
  AlertCircle,
  BarChart3,
  Activity,
} from "lucide-react"

export default function AdminDashboard() {
  const { admin, loading } = useAdmin()

  if (loading) {
    return (
      <div className="min-h-screen bg-zinc-950 flex items-center justify-center">
        <div className="text-white">Loading...</div>
      </div>
    )
  }

  if (!admin) {
    return (
      <div className="min-h-screen bg-zinc-950 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-white mb-4">Access Denied</h1>
          <p className="text-zinc-400">You need admin privileges to access this page.</p>
        </div>
      </div>
    )
  }

  const stats = [
    {
      title: "Total Revenue",
      value: "$124,500",
      change: "+12.5%",
      icon: DollarSign,
      color: "text-green-500",
    },
    {
      title: "Total Users",
      value: "2,847",
      change: "+8.2%",
      icon: Users,
      color: "text-blue-500",
    },
    {
      title: "Active Organizers",
      value: "156",
      change: "+5.1%",
      icon: Building2,
      color: "text-purple-500",
    },
    {
      title: "Total Events",
      value: "892",
      change: "+15.3%",
      icon: Calendar,
      color: "text-orange-500",
    },
    {
      title: "Pending Approvals",
      value: "23",
      change: "-2.1%",
      icon: Clock,
      color: "text-yellow-500",
    },
  ]

  const topOrganizers = [
    { name: "EventPro Inc", revenue: "$45,200", events: 28, status: "active" },
    { name: "MegaEvents Ltd", revenue: "$38,900", events: 22, status: "active" },
    { name: "CityLife Events", revenue: "$32,100", events: 19, status: "active" },
    { name: "Premium Shows", revenue: "$28,700", events: 15, status: "active" },
    { name: "Local Gatherings", revenue: "$21,400", events: 12, status: "suspended" },
  ]

  const recentActivity = [
    { action: "New organizer registered", user: "Sarah Johnson", time: "2 minutes ago", type: "user" },
    { action: "Event approved", event: "Summer Music Festival", time: "15 minutes ago", type: "approval" },
    { action: "Large ticket purchase", amount: "$2,400", time: "1 hour ago", type: "sale" },
    { action: "Organizer suspended", user: "BadEvents Co", time: "2 hours ago", type: "warning" },
    { action: "New event submitted", event: "Tech Conference 2024", time: "3 hours ago", type: "event" },
  ]

  return (
    <AdminLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-white">Admin Dashboard</h1>
            <p className="text-zinc-400 mt-1">Welcome back, {admin.name}</p>
          </div>
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="border-red-500 text-red-500">
              Admin Access
            </Badge>
          </div>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
          {stats.map((stat, index) => (
            <Card key={index} className="bg-zinc-900 border-zinc-800">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-zinc-400">{stat.title}</p>
                    <p className="text-2xl font-bold text-white mt-1">{stat.value}</p>
                    <p className={`text-sm mt-1 ${stat.change.startsWith("+") ? "text-green-500" : "text-red-500"}`}>
                      {stat.change} from last month
                    </p>
                  </div>
                  <div className={`p-3 rounded-lg bg-zinc-800 ${stat.color}`}>
                    <stat.icon className="h-6 w-6" />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Top Organizers */}
          <Card className="bg-zinc-900 border-zinc-800">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Top Performing Organizers
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {topOrganizers.map((organizer, index) => (
                  <div key={index} className="flex items-center justify-between p-3 rounded-lg bg-zinc-800">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-zinc-700 rounded-full flex items-center justify-center">
                        <Building2 className="h-5 w-5 text-zinc-400" />
                      </div>
                      <div>
                        <p className="font-medium text-white">{organizer.name}</p>
                        <p className="text-sm text-zinc-400">{organizer.events} events</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-medium text-white">{organizer.revenue}</p>
                      <Badge variant={organizer.status === "active" ? "default" : "destructive"} className="text-xs">
                        {organizer.status}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Recent Activity */}
          <Card className="bg-zinc-900 border-zinc-800">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Recent Activity
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentActivity.map((activity, index) => (
                  <div key={index} className="flex items-start gap-3 p-3 rounded-lg bg-zinc-800">
                    <div
                      className={`p-2 rounded-full ${
                        activity.type === "user"
                          ? "bg-blue-500/20 text-blue-500"
                          : activity.type === "approval"
                            ? "bg-green-500/20 text-green-500"
                            : activity.type === "sale"
                              ? "bg-purple-500/20 text-purple-500"
                              : activity.type === "warning"
                                ? "bg-red-500/20 text-red-500"
                                : "bg-orange-500/20 text-orange-500"
                      }`}
                    >
                      {activity.type === "user" && <Users className="h-4 w-4" />}
                      {activity.type === "approval" && <CheckCircle className="h-4 w-4" />}
                      {activity.type === "sale" && <DollarSign className="h-4 w-4" />}
                      {activity.type === "warning" && <AlertCircle className="h-4 w-4" />}
                      {activity.type === "event" && <Calendar className="h-4 w-4" />}
                    </div>
                    <div className="flex-1">
                      <p className="text-sm text-white">{activity.action}</p>
                      <p className="text-xs text-zinc-400 mt-1">
                        {activity.user || activity.event || activity.amount} • {activity.time}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <Card className="bg-zinc-900 border-zinc-800">
          <CardHeader>
            <CardTitle className="text-white">Quick Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Button className="bg-red-600 hover:bg-red-700 text-white">
                <Users className="h-4 w-4 mr-2" />
                Manage Users
              </Button>
              <Button variant="outline" className="border-zinc-700 text-white hover:bg-zinc-800 bg-transparent">
                <Calendar className="h-4 w-4 mr-2" />
                Review Events
              </Button>
              <Button variant="outline" className="border-zinc-700 text-white hover:bg-zinc-800 bg-transparent">
                <BarChart3 className="h-4 w-4 mr-2" />
                View Analytics
              </Button>
              <Button variant="outline" className="border-zinc-700 text-white hover:bg-zinc-800 bg-transparent">
                <Building2 className="h-4 w-4 mr-2" />
                Manage Organizers
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  )
}
