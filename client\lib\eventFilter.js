/**
 * EventFilter - OOP class for client-side event filtering
 * Handles all filtering logic on the frontend for better performance
 */
class EventFilter {
  constructor() {
    this.events = [];
    this.filteredEvents = [];
    this.filters = {
      status: 'upcoming',
      genreIds: [],
      locationIds: [],
      search: '',
      sortBy: 'startDate',
      sortOrder: 'asc'
    };
    this.pagination = {
      page: 1,
      limit: 12
    };
  }

  // Set all events data
  setEvents(events) {
    this.events = events;
    this.applyFilters();
  }

  // Update filters and reapply
  updateFilters(newFilters) {
    this.filters = { ...this.filters, ...newFilters };
    this.pagination.page = 1; // Reset to first page when filters change
    this.applyFilters();
  }

  // Update pagination
  updatePagination(newPagination) {
    this.pagination = { ...this.pagination, ...newPagination };
  }

  // Apply all filters to events
  applyFilters() {
    let filtered = [...this.events];

    // Apply status filter (live, upcoming, past)
    filtered = this.filterByStatus(filtered, this.filters.status);

    // Apply genre filter
    if (this.filters.genreIds.length > 0) {
      filtered = this.filterByGenres(filtered, this.filters.genreIds);
    }

    // Apply location filter
    if (this.filters.locationIds.length > 0) {
      filtered = this.filterByLocations(filtered, this.filters.locationIds);
    }

    // Apply search filter
    if (this.filters.search.trim()) {
      filtered = this.filterBySearch(filtered, this.filters.search.trim());
    }

    // Apply sorting
    filtered = this.sortEvents(filtered, this.filters.sortBy, this.filters.sortOrder);

    this.filteredEvents = filtered;
  }

  // Filter events by status (live, upcoming, past)
  filterByStatus(events, status) {
    const now = new Date();

    return events.filter(event => {
      const ticketsSaleStart = new Date(event.ticketsSaleStart);
      const ticketsSaleEnd = new Date(event.ticketsSaleEnd);
      const endDate = new Date(event.endDate);

      switch (status) {
        case 'live':
          // Event is live when current time is between tickets_sale_start and tickets_sale_end
          return now >= ticketsSaleStart && now <= ticketsSaleEnd;
        case 'upcoming':
          // Event is upcoming when current time is less than tickets_sale_start
          return now < ticketsSaleStart;
        case 'past':
          // Event is past when current time is greater than event end_date
          return now > endDate;
        default:
          return true;
      }
    });
  }

  // Filter events by genres
  filterByGenres(events, genreIds) {
    return events.filter(event => 
      event.genre && genreIds.includes(event.genre.id)
    );
  }

  // Filter events by locations
  filterByLocations(events, locationIds) {
    return events.filter(event => 
      event.location && locationIds.includes(event.location.id)
    );
  }

  // Filter events by search query
  filterBySearch(events, searchQuery) {
    const query = searchQuery.toLowerCase();
    
    return events.filter(event => {
      const title = event.title?.toLowerCase() || '';
      const description = event.description?.toLowerCase() || '';
      const venueName = event.venueName?.toLowerCase() || '';
      const genreName = event.genre?.name?.toLowerCase() || '';
      const locationCity = event.location?.city?.toLowerCase() || '';
      const organizerName = event.organizer?.name?.toLowerCase() || '';

      return title.includes(query) ||
             description.includes(query) ||
             venueName.includes(query) ||
             genreName.includes(query) ||
             locationCity.includes(query) ||
             organizerName.includes(query);
    });
  }

  // Sort events
  sortEvents(events, sortBy, sortOrder) {
    return events.sort((a, b) => {
      let aValue, bValue;

      switch (sortBy) {
        case 'startDate':
          aValue = new Date(a.startDate);
          bValue = new Date(b.startDate);
          break;
        case 'title':
          aValue = a.title?.toLowerCase() || '';
          bValue = b.title?.toLowerCase() || '';
          break;
        case 'price':
          aValue = a.ticketTypes && a.ticketTypes.length > 0 
            ? Math.min(...a.ticketTypes.map(tt => tt.price))
            : 0;
          bValue = b.ticketTypes && b.ticketTypes.length > 0 
            ? Math.min(...b.ticketTypes.map(tt => tt.price))
            : 0;
          break;
        case 'location':
          aValue = a.location?.city?.toLowerCase() || '';
          bValue = b.location?.city?.toLowerCase() || '';
          break;
        default:
          aValue = new Date(a.startDate);
          bValue = new Date(b.startDate);
      }

      if (sortOrder === 'desc') {
        return aValue < bValue ? 1 : aValue > bValue ? -1 : 0;
      } else {
        return aValue > bValue ? 1 : aValue < bValue ? -1 : 0;
      }
    });
  }

  // Get paginated results
  getPaginatedEvents() {
    const startIndex = (this.pagination.page - 1) * this.pagination.limit;
    const endIndex = startIndex + this.pagination.limit;
    
    return {
      events: this.filteredEvents.slice(startIndex, endIndex),
      pagination: {
        page: this.pagination.page,
        limit: this.pagination.limit,
        total: this.filteredEvents.length,
        totalPages: Math.ceil(this.filteredEvents.length / this.pagination.limit)
      }
    };
  }

  // Get all filtered events (no pagination)
  getFilteredEvents() {
    return this.filteredEvents;
  }

  // Get events count by status
  getEventCountsByStatus() {
    const now = new Date();
    const counts = { live: 0, upcoming: 0, past: 0 };

    this.events.forEach(event => {
      const ticketsSaleStart = new Date(event.ticketsSaleStart);
      const ticketsSaleEnd = new Date(event.ticketsSaleEnd);
      const endDate = new Date(event.endDate);

      if (now >= ticketsSaleStart && now <= ticketsSaleEnd) {
        counts.live++;
      } else if (now < ticketsSaleStart) {
        counts.upcoming++;
      } else if (now > endDate) {
        counts.past++;
      }
    });

    return counts;
  }

  // Reset all filters
  resetFilters() {
    this.filters = {
      status: 'upcoming',
      genreIds: [],
      locationIds: [],
      search: '',
      sortBy: 'startDate',
      sortOrder: 'asc'
    };
    this.pagination.page = 1;
    this.applyFilters();
  }

  // Get current filter state
  getFilters() {
    return { ...this.filters };
  }

  // Get current pagination state
  getPagination() {
    return { ...this.pagination };
  }

  // Get total events count
  getTotalEventsCount() {
    return this.events.length;
  }

  // Get filtered events count
  getFilteredEventsCount() {
    return this.filteredEvents.length;
  }
}

export default EventFilter;
