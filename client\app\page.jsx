"use client"

import { useState, useRef, useEffect } from "react"
import { motion } from "framer-motion"
import Navbar from "@/components/navbar"
import HeroCarousel from "@/components/hero-carousel"
import EventGrid from "@/components/event-grid"
import Benefits from "@/components/benefits"
import Footer from "@/components/footer"
import MobileNav from "@/components/mobile-nav"
import FilterSection from "@/components/filter-section"
import { useMediaQuery } from "@/hooks/use-media-query"
import CartModal from "@/components/cart-modal"
import AuthModal from "@/components/auth-modal"
import { useCart } from "@/context/cart-context"
import { Calendar, MapPin } from "lucide-react"
import { useRouter } from "next/navigation"
import eventManager from "@/lib/eventManager"

// Mock data (fallback)
import { events as mockEvents, organizers } from "@/data/mock-data"

export default function Home() {
  const [showAuthModal, setShowAuthModal] = useState(false)
  const [authMode, setAuthMode] = useState("login") // "login" or "register"
  const [filteredEvents, setFilteredEvents] = useState([])
  const [allEvents, setAllEvents] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const isMobile = useMediaQuery("(max-width: 768px)")
  const { isCartOpen } = useCart()
  const router = useRouter()

  // Load events on component mount
  useEffect(() => {
    const loadEvents = async () => {
      try {
        setLoading(true)
        setError(null)

        // Try to load real events from database
        await eventManager.loadAllEvents()
        const state = eventManager.getState()

        if (state.allEvents && state.allEvents.length > 0) {
          setAllEvents(state.allEvents)
          setFilteredEvents(state.allEvents)
        } else {
          // Fallback to mock data if no real events
          setAllEvents(mockEvents)
          setFilteredEvents(mockEvents)
        }
      } catch (err) {
        console.error('Failed to load events:', err)
        setError(err.message)
        // Fallback to mock data on error
        setAllEvents(mockEvents)
        setFilteredEvents(mockEvents)
      } finally {
        setLoading(false)
      }
    }

    loadEvents()
  }, [])

  // Subscribe to event manager updates
  useEffect(() => {
    const handleEventManagerUpdate = (data) => {
      if (data.type === 'EVENTS_CHANGED') {
        setAllEvents(eventManager.getState().allEvents)
        setFilteredEvents(eventManager.getState().allEvents)
      } else if (data.type === 'ERROR_CHANGED') {
        setError(data.error)
      } else if (data.type === 'LOADING_CHANGED') {
        setLoading(data.loading)
      }
    }

    eventManager.subscribe(handleEventManagerUpdate)

    return () => {
      eventManager.unsubscribe(handleEventManagerUpdate)
    }
  }, [])

  const handleFilterChange = (filters) => {
    // Filter events based on selected filters
    let filtered = [...allEvents]

    if (filters.genre && filters.genre !== "all") {
      filtered = filtered.filter((event) => {
        const eventGenre = typeof event.genre === 'string' ? event.genre : event.genre?.name
        return eventGenre === filters.genre
      })
    }

    if (filters.location && filters.location !== "all") {
      filtered = filtered.filter((event) => {
        const eventCity = event.location?.city || 'Unknown'
        return eventCity === filters.location
      })
    }

    if (filters.date) {
      const today = new Date()
      const nextWeek = new Date(today)
      nextWeek.setDate(today.getDate() + 7)
      const nextMonth = new Date(today)
      nextMonth.setMonth(today.getMonth() + 1)

      if (filters.date === "today") {
        filtered = filtered.filter((event) => {
          const eventDate = new Date(event.startDate || event.date)
          return eventDate.toDateString() === today.toDateString()
        })
      } else if (filters.date === "week") {
        filtered = filtered.filter((event) => {
          const eventDate = new Date(event.startDate || event.date)
          return eventDate >= today && eventDate <= nextWeek
        })
      } else if (filters.date === "month") {
        filtered = filtered.filter((event) => {
          const eventDate = new Date(event.startDate || event.date)
          return eventDate >= today && eventDate <= nextMonth
        })
      }
    }

    if (filters.search) {
      filtered = filtered.filter(
        (event) =>
          event.title.toLowerCase().includes(filters.search.toLowerCase()) ||
          (event.description && event.description.toLowerCase().includes(filters.search.toLowerCase())) ||
          (event.location?.city && event.location.city.toLowerCase().includes(filters.search.toLowerCase())) ||
          (event.venueName && event.venueName.toLowerCase().includes(filters.search.toLowerCase()))
      )
    }

    setFilteredEvents(filtered)
  }

  const openAuthModal = (mode) => {
    setAuthMode(mode)
    setShowAuthModal(true)
  }

  // Helper function to filter events by status using the same logic as EventFilter
  const filterEventsByStatus = (events, status) => {
    const now = new Date()

    return events.filter(event => {
      const ticketsSaleStart = new Date(event.ticketsSaleStart || event.startDate || event.date)
      const ticketsSaleEnd = new Date(event.ticketsSaleEnd || event.endDate || event.date)
      const endDate = new Date(event.endDate || event.date)

      switch (status) {
        case 'live':
          // Event is live when current time is between tickets_sale_start and tickets_sale_end
          return now >= ticketsSaleStart && now <= ticketsSaleEnd
        case 'upcoming':
          // Event is upcoming when current time is less than tickets_sale_start
          return now < ticketsSaleStart
        case 'past':
          // Event is past when current time is greater than event end_date
          return now > endDate
        default:
          return true
      }
    })
  }

  const liveEvents = filterEventsByStatus(allEvents, 'live')
  const pastEvents = filterEventsByStatus(allEvents, 'past')

  return (
    <div className="min-h-screen bg-zinc-950 text-white">
      <Navbar onLoginClick={() => openAuthModal("login")} onRegisterClick={() => openAuthModal("register")} />

      <main className="pb-20 md:pb-0">
        <HeroCarousel events={liveEvents.slice(0, 5)} loading={loading} error={error} />

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="container mx-auto px-4 py-8"
        >
          <FilterSection onFilterChange={handleFilterChange} />

          <section className="my-12">
            <h2 className="text-2xl font-bold mb-6">Live Events</h2>
            <EventGrid
              events={liveEvents.length > 0 ? liveEvents : allEvents.slice(0, 8)}
              loading={loading}
              error={error}
            />
          </section>

          <section className="my-12">
            <h2 className="text-2xl font-bold mb-6">Past Events</h2>
            <div className="relative overflow-hidden">
              {/* Left fade effect */}
              <div className="absolute left-0 top-0 bottom-0 w-16 z-10 bg-gradient-to-r from-zinc-950 to-transparent pointer-events-none"></div>

              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.5 }}
                className="relative"
              >
                {pastEvents.length > 0 ? (
                  <AutoScrollCarousel events={pastEvents} loading={loading} error={error} />
                ) : (
                  <AutoScrollCarousel events={allEvents.slice(8, 16)} loading={loading} error={error} />
                )}
              </motion.div>

              {/* Right fade effect */}
              <div className="absolute right-0 top-0 bottom-0 w-16 z-10 bg-gradient-to-l from-zinc-950 to-transparent pointer-events-none"></div>
            </div>
          </section>

          <section className="my-12">
            <h2 className="text-2xl font-bold mb-6">Popular Organizers</h2>
            <div className="relative overflow-hidden">
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.5 }}
                className="relative"
              >
                <OrganizersScrollCarousel organizers={organizers} />
              </motion.div>
            </div>
          </section>

          <Benefits />
        </motion.div>
      </main>

      <Footer />

      {isMobile && <MobileNav />}

      {isCartOpen && <CartModal />}

      {showAuthModal && (
        <AuthModal
          mode={authMode}
          onClose={() => setShowAuthModal(false)}
          onSwitchMode={() => setAuthMode(authMode === "login" ? "register" : "login")}
        />
      )}
    </div>
  )
}

// Auto-scrolling carousel component
const AutoScrollCarousel = ({ events }) => {
  const carouselRef = useRef(null)
  const [width, setWidth] = useState(0)
  const [isDragging, setIsDragging] = useState(false)
  const router = useRouter()

  useEffect(() => {
    if (carouselRef.current) {
      setWidth(carouselRef.current.scrollWidth - carouselRef.current.offsetWidth)
    }
  }, [events])

  useEffect(() => {
    let intervalId

    if (!isDragging) {
      let scrollPosition = 0
      const scrollStep = 1

      intervalId = setInterval(() => {
        if (carouselRef.current) {
          scrollPosition += scrollStep

          // Reset when reaching the end
          if (scrollPosition >= width) {
            scrollPosition = 0
          }

          carouselRef.current.scrollLeft = scrollPosition
        }
      }, 30)
    }

    return () => {
      if (intervalId) {
        clearInterval(intervalId)
      }
    }
  }, [width, isDragging])

  return (
    <motion.div
      ref={carouselRef}
      className="cursor-grab overflow-hidden"
      whileTap={{ cursor: "grabbing" }}
      onMouseDown={() => setIsDragging(true)}
      onMouseUp={() => setIsDragging(false)}
      onMouseLeave={() => setIsDragging(false)}
    >
      <motion.div
        className="flex"
        drag="x"
        dragConstraints={{ right: 0, left: -width }}
        onDragStart={() => setIsDragging(true)}
        onDragEnd={() => setIsDragging(false)}
      >
        {events.map((event) => {
          // Helper functions for data compatibility
          const getEventImage = (event) => {
            return event.bannerImage || event.image || `/placeholder.svg?height=160&width=288`
          }

          const getEventGenre = (event) => {
            if (typeof event.genre === 'string') {
              return event.genre
            }
            return event.genre?.name || 'Event'
          }

          const getEventDate = (event) => {
            return event.startDate || event.date
          }

          const getEventLocation = (event) => {
            // Handle mock data structure
            if (event.location && typeof event.location === 'object' && event.location.venue) {
              return {
                venue: event.location.venue,
                city: event.location.city
              }
            }

            // Handle real database structure
            const venue = event.venueName || event.location?.venueName || 'TBA'
            const city = event.location?.city || 'TBA'

            return { venue, city }
          }

          const getEventPrice = (event) => {
            // Handle mock data structure
            if (typeof event.price === 'number') {
              return event.price.toFixed(2)
            }

            // Handle real database structure - get minimum ticket price
            if (event.ticketTypes && event.ticketTypes.length > 0) {
              const minPrice = Math.min(...event.ticketTypes.map(tt => parseFloat(tt.price) || 0))
              return minPrice.toFixed(2)
            }

            return "49.99"
          }

          const eventImage = getEventImage(event)
          const eventGenre = getEventGenre(event)
          const eventDate = getEventDate(event)
          const eventLocation = getEventLocation(event)
          const eventPrice = getEventPrice(event)

          return (
            <motion.div key={event.id} className="min-w-[280px] h-full p-2" whileHover={{ scale: 1.03 }}>
              <div
                className="bg-zinc-800 rounded-lg overflow-hidden h-full cursor-pointer"
                onClick={() => router.push(`/events/${event.id}`)}
              >
                <div className="h-40 relative">
                  <img
                    src={eventImage}
                    alt={event.title}
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      e.target.src = `/placeholder.svg?height=160&width=288`
                    }}
                  />
                  <div className="absolute top-0 right-0 bg-red-600 text-white px-2 py-1 text-xs">
                    {eventGenre}
                  </div>
                </div>

                <div className="p-4">
                  <h3 className="font-bold mb-2 line-clamp-1">{event.title}</h3>

                  <div className="space-y-1 mb-3">
                    <div className="flex items-center text-zinc-400 text-xs">
                      <Calendar className="h-3 w-3 mr-1" />
                      <span>
                        {new Date(eventDate).toLocaleDateString("en-US", {
                          month: "short",
                          day: "numeric",
                          year: "numeric",
                        })}
                      </span>
                    </div>
                    <div className="flex items-center text-zinc-400 text-xs">
                      <MapPin className="h-3 w-3 mr-1" />
                      <span className="truncate">
                        {eventLocation.venue}, {eventLocation.city}
                      </span>
                    </div>
                  </div>

                  <div className="flex justify-between items-center">
                    <span className="text-sm font-bold">${eventPrice}</span>
                    <span className="text-xs text-zinc-400">Starting from</span>
                  </div>
                </div>
              </div>
            </motion.div>
          )
        })}
      </motion.div>
    </motion.div>
  )
}

// Auto-scrolling carousel component for organizers
const OrganizersScrollCarousel = ({ organizers }) => {
  const carouselRef = useRef(null)
  const [width, setWidth] = useState(0)
  const [isDragging, setIsDragging] = useState(false)

  useEffect(() => {
    if (carouselRef.current) {
      setWidth(carouselRef.current.scrollWidth - carouselRef.current.offsetWidth)
    }
  }, [organizers])

  useEffect(() => {
    let intervalId

    if (!isDragging) {
      let scrollPosition = 0
      const scrollStep = 1

      intervalId = setInterval(() => {
        if (carouselRef.current) {
          scrollPosition += scrollStep

          // Reset when reaching the end
          if (scrollPosition >= width) {
            scrollPosition = 0
          }

          carouselRef.current.scrollLeft = scrollPosition
        }
      }, 30)
    }

    return () => {
      if (intervalId) {
        clearInterval(intervalId)
      }
    }
  }, [width, isDragging])

  return (
    <motion.div
      ref={carouselRef}
      className="cursor-grab overflow-hidden"
      whileTap={{ cursor: "grabbing" }}
      onMouseDown={() => setIsDragging(true)}
      onMouseUp={() => setIsDragging(false)}
      onMouseLeave={() => setIsDragging(false)}
    >
      <motion.div
        className="flex"
        drag="x"
        dragConstraints={{ right: 0, left: -width }}
        onDragStart={() => setIsDragging(true)}
        onDragEnd={() => setIsDragging(false)}
      >
        {organizers.map((organizer, index) => (
          <motion.div key={index} className="min-w-[200px] h-full p-2" whileHover={{ scale: 1.03 }}>
            <div className="bg-zinc-800 rounded-lg p-4 text-center hover:bg-zinc-700 transition-colors h-[180px] flex flex-col items-center justify-center">
              <div className="w-16 h-16 mx-auto bg-zinc-600 rounded-full mb-3 overflow-hidden">
                <img
                  src={organizer.logo || `/placeholder.svg?height=64&width=64`}
                  alt={organizer.name}
                  className="w-full h-full object-cover"
                />
              </div>
              <h3 className="font-medium text-sm line-clamp-1">{organizer.name}</h3>
              <p className="text-xs text-zinc-400">{organizer.eventCount} events</p>
            </div>
          </motion.div>
        ))}
      </motion.div>
    </motion.div>
  )
}
