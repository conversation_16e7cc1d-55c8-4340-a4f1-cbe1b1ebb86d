const { PrismaClient } = require("@prisma/client");
const SSLService = require("../services/sslService");
const TicketService = require("../services/ticketService");

const prisma = new PrismaClient();

class SSLController {
  constructor() {
    this.sslService = SSLService.getInstance();
    this.ticketService = new TicketService();
  }

  initiatePayment = async (req, res) => {
    try {
      const { orderId } = req.body;
      console.log("Received order ID:", orderId);

      const orderData = orderId;
      // Validate input
      if (!orderId) {
        return res.status(400).json({
          success: false,
          message: "Order ID is required",
          data: null,
        });
      }

      // Validate user authorization
      const userId = req.user.id || req.user.user_id;
      if (!userId) {
        return res.status(401).json({
          success: false,
          message: "User authentication required",
          data: null,
        });
      }

      // Get complete order data with all necessary includes
      // const orderData = await prisma.orders.findUnique({
      //   where: {
      //     order_id: orderId.order_id,
      //     user_id: userId,
      //     payment_status: "pending",
      //   },
      //   include: {
      //     orderitems: {
      //       include: {
      //         tickettypes: {
      //           include: {
      //             events: true,
      //           },
      //         },
      //       },
      //     },
      //     users: {
      //       include: {
      //         masteraccounts: true,
      //       },
      //     },
      //   },
      // });

      console.log("Order Data:", orderData);

      if (!orderData) {
        return res.status(404).json({
          success: false,
          message: "Order not found or already processed",
          data: null,
        });
      }

      console.log(orderData.orderitems);
      // Validate order has items
      if (!orderData.orderitems || orderData.orderitems.length === 0) {
        return res.status(400).json({
          success: false,
          message: "Order has no items",
          data: null,
        });
      }

      // Initiate payment through SSL service
      const response = await this.sslService.initiatePayment(orderData);

      if (response.success) {
        res.json(response);
      } else {
        res.status(400).json(response);
      }
    } catch (error) {
      console.error("Error initiating payment:", error);
      res.status(500).json({
        success: false,
        message: error.message || "Failed to initiate payment",
        data: null,
      });
    }
  };

  validatePayment = async (req, res) => {
    try {
      const validationData = req.body;

      // Validate input
      if (!validationData || !validationData.tran_id) {
        return res.status(400).json({
          success: false,
          message: "Transaction ID is required for validation",
          data: null,
        });
      }

      const response = await this.sslService.validatePayment(validationData);

      if (response.success) {
        res.json(response);
      } else {
        res.status(400).json(response);
      }
    } catch (error) {
      console.error("Error validating payment:", error);
      res.status(500).json({
        success: false,
        message: error.message || "Failed to validate payment",
        data: null,
      });
    }
  };

  // Handle payment success callback
  paymentSuccess = async (req, res) => {
    try {
      const paymentData = req.body;
      const response = await this.sslService.handlePaymentSuccess(paymentData);

      if (response.success) {
        // Create tickets after successful payment validation
        try {
          await this.createTicketsForOrder(response.data.order);
          console.log(
            `Tickets created successfully for order ${response.data.order.order_id}`
          );
        } catch (ticketError) {
          console.error("Error creating tickets after payment:", ticketError);
          // Don't fail the payment success flow if ticket creation fails
          // Tickets can be created later via the success page as fallback
        }

        // Redirect to success page with order info
        res.redirect(
          `${process.env.CLIENT_URL}/payment/success?orderId=${response.data.order.order_id}&transactionId=${response.data.transactionId}`
        );
      } else {
        res.redirect(
          `${process.env.CLIENT_URL}/payment/fail?error=${encodeURIComponent(
            response.message
          )}`
        );
      }
    } catch (error) {
      console.error("Error handling payment success:", error);
      res.redirect(
        `${process.env.CLIENT_URL}/payment/fail?error=${encodeURIComponent(
          "Payment processing failed"
        )}`
      );
    }
  };

  // Handle payment failure callback
  paymentFailure = async (req, res) => {
    try {
      const paymentData = req.body;
      const response = await this.sslService.handlePaymentFailure(paymentData);

      res.redirect(
        `${process.env.CLIENT_URL}/payment/fail?error=${encodeURIComponent(
          response.message
        )}`
      );
    } catch (error) {
      console.error("Error handling payment failure:", error);
      res.redirect(
        `${process.env.CLIENT_URL}/payment/fail?error=${encodeURIComponent(
          "Payment failed"
        )}`
      );
    }
  };

  // Handle payment cancellation
  paymentCancel = async (req, res) => {
    try {
      const paymentData = req.body;

      if (paymentData && paymentData.tran_id) {
        // Update order status to cancelled if needed
        const order = await prisma.orders.findFirst({
          where: { transaction_id: paymentData.tran_id },
        });

        if (order) {
          await prisma.orders.update({
            where: { order_id: order.order_id },
            data: { payment_status: "pending" }, // Keep as pending for retry
          });
        }
      }

      res.redirect(`${process.env.CLIENT_URL}/checkout?cancelled=true`);
    } catch (error) {
      console.error("Error handling payment cancellation:", error);
      res.redirect(
        `${process.env.CLIENT_URL}/checkout?error=${encodeURIComponent(
          "Payment cancelled"
        )}`
      );
    }
  };

  // Handle IPN (Instant Payment Notification)
  handleIPN = async (req, res) => {
    try {
      const ipnData = req.body;

      // Validate IPN data
      const validationResult = await this.sslService.validatePayment(ipnData);

      if (validationResult.success) {
        // IPN validation successful
        console.log("IPN validation successful:", validationResult.data);
        res.status(200).send("IPN processed successfully");
      } else {
        console.error("IPN validation failed:", validationResult.message);
        res.status(400).send("IPN validation failed");
      }
    } catch (error) {
      console.error("Error handling IPN:", error);
      res.status(500).send("IPN processing failed");
    }
  };

  /**
   * Create tickets for an order after successful payment
   * This method replicates the ticket creation logic from the client-side success page
   */
  createTicketsForOrder = async (orderData) => {
    try {
      if (
        !orderData ||
        !orderData.orderitems ||
        orderData.orderitems.length === 0
      ) {
        throw new Error("Invalid order data for ticket creation");
      }

      // Transform order data to selectedTickets format for createTickets()
      const selectedTickets = [];
      let eventId = null;

      for (const orderItem of orderData.items) {
        selectedTickets.push({
          ticketTypeId: orderItem.ticket_type_id,
          quantity: orderItem.quantity,
          price: parseFloat(orderItem.unit_price),
          name: orderItem.tickettypes.name,
        });

        // Get eventId from the first order item
        if (!eventId) {
          eventId = orderItem.tickettypes.events.event_id;
        }
      }

      console.log("Creating tickets for order:", orderData.order_id);
      console.log("Selected Tickets:", selectedTickets);
      console.log("Event ID:", eventId);

      // Create tickets using the ticket service with existing order ID
      // Note: We don't have attendee info in the server callback, so we pass empty array
      // The success page can still handle attendee info if needed
      const result = await this.ticketService.createTickets(
        selectedTickets,
        [], // Empty attendee info - will be handled by success page if needed
        eventId,
        orderData.order_id // Use existing order ID
      );

      if (!result.success) {
        throw new Error(result.message || "Failed to create tickets");
      }

      console.log(
        `Successfully created ${result.data.tickets.length} tickets for order ${orderData.order_id}`
      );
      return result;
    } catch (error) {
      console.error("Error in createTicketsForOrder:", error);
      throw error;
    }
  };
}

module.exports = SSLController;
